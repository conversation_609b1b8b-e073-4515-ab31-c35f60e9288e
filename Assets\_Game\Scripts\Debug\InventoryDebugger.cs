using UnityEngine;
using System.IO;
using System.Linq;
using System;

/// <summary>
/// Debug tool to help investigate inventory wipe issues
/// </summary>
public class InventoryDebugger : MonoBehaviour
{
    [Header("Inventory Debugger")]
    [SerializeField] private KeyCode debugKey = KeyCode.F12;
    [SerializeField] private KeyCode forceLoadKey = KeyCode.F9;
    [SerializeField] private KeyCode forceSaveKey = KeyCode.F8;
    
    private EquipmentManager equipmentManager;
    private PersistenceManager persistenceManager;
    
    private void Start()
    {
        equipmentManager = FindObjectOfType<EquipmentManager>();
        persistenceManager = PersistenceManager.Instance ?? FindObjectOfType<PersistenceManager>();
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(debugKey))
        {
            DebugCurrentState();
        }
        
        if (Input.GetKeyDown(forceLoadKey))
        {
            ForceLoadInventory();
        }
        
        if (Input.GetKeyDown(forceSaveKey))
        {
            ForceSaveInventory();
        }
    }
    
    [ContextMenu("Debug Current State")]
    public void DebugCurrentState()
    {
        Debug.Log("=== INVENTORY DEBUG STATE ===");
        
        if (equipmentManager == null)
        {
            Debug.LogError("[INVENTORY_DEBUG] EquipmentManager is NULL!");
        }
        else
        {
            Debug.Log($"[INVENTORY_DEBUG] EquipmentManager found: {equipmentManager.name}");
            
            var slots = equipmentManager.GetEquipmentSlots();
            Debug.Log($"[INVENTORY_DEBUG] Equipment slots count: {slots.Count()}");
            
            foreach (var slot in slots)
            {
                string itemName = slot.equippedItem?.itemName ?? "NONE";
                int containerItems = slot.storageContainer?.GetItems()?.Count ?? 0;
                Debug.Log($"[INVENTORY_DEBUG] Slot {slot.slotType}: '{itemName}' with {containerItems} items in container");
            }
        }
        
        if (persistenceManager == null)
        {
            Debug.LogError("[INVENTORY_DEBUG] PersistenceManager is NULL!");
        }
        else
        {
            Debug.Log($"[INVENTORY_DEBUG] PersistenceManager found: {persistenceManager.name}");
            Debug.Log($"[INVENTORY_DEBUG] Initialization complete: {persistenceManager.IsInitializationComplete}");
        }
        
        // Force log current inventory state
        if (equipmentManager != null)
        {
            equipmentManager.ForceLogInventoryState("DEBUG_MANUAL_CHECK");
        }
    }
    
    [ContextMenu("Force Save Inventory")]
    public void ForceSaveInventory()
    {
        Debug.Log("[INVENTORY_DEBUG] Force saving inventory...");
        
        if (persistenceManager != null)
        {
            persistenceManager.SaveInventoryAndEquipmentNow();
            Debug.Log("[INVENTORY_DEBUG] Force save completed");
        }
        else
        {
            Debug.LogError("[INVENTORY_DEBUG] Cannot force save - PersistenceManager is null");
        }
    }
    
    [ContextMenu("Force Load Inventory")]
    public void ForceLoadInventory()
    {
        Debug.Log("[INVENTORY_DEBUG] This would require calling private methods - check console for load logs during game start");
    }
    
    [ContextMenu("Show Save File Info")]
    public void ShowSaveFileInfo()
    {
        string saveFilePath = Path.Combine(Application.persistentDataPath, "progression_data.json");
        
        if (File.Exists(saveFilePath))
        {
            try
            {
                string saveContent = File.ReadAllText(saveFilePath);
                Debug.Log($"[INVENTORY_DEBUG] Save file exists at: {saveFilePath}");
                Debug.Log($"[INVENTORY_DEBUG] Save file size: {saveContent.Length} characters");
                
                // Look for equipment data in the save file
                if (saveContent.Contains("equipmentData"))
                {
                    Debug.Log("[INVENTORY_DEBUG] Save file contains equipmentData");
                    
                    // Try to find equipment entries
                    int equipmentIndex = saveContent.IndexOf("equipmentData");
                    if (equipmentIndex >= 0)
                    {
                        string equipmentSection = saveContent.Substring(equipmentIndex, Mathf.Min(500, saveContent.Length - equipmentIndex));
                        Debug.Log($"[INVENTORY_DEBUG] Equipment section preview: {equipmentSection}");
                    }
                }
                else
                {
                    Debug.LogWarning("[INVENTORY_DEBUG] Save file does NOT contain equipmentData!");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[INVENTORY_DEBUG] Error reading save file: {e.Message}");
            }
        }
        else
        {
            Debug.LogWarning($"[INVENTORY_DEBUG] Save file does not exist at: {saveFilePath}");
        }
    }
    
    [ContextMenu("Clear All Logs")]
    public void ClearAllLogs()
    {
        // Clear inventory log
        string inventoryLogPath = Path.Combine(Application.persistentDataPath, "inventory_state.log");
        if (File.Exists(inventoryLogPath))
        {
            File.Delete(inventoryLogPath);
            Debug.Log("[INVENTORY_DEBUG] Cleared inventory log");
        }
        
        // Clear scene transition log
        string transitionLogPath = Path.Combine(Application.persistentDataPath, "scene_transition.log");
        if (File.Exists(transitionLogPath))
        {
            File.Delete(transitionLogPath);
            Debug.Log("[INVENTORY_DEBUG] Cleared scene transition log");
        }
    }
    
    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 200));
        GUILayout.Label("Inventory Debugger", new GUIStyle(GUI.skin.label) { fontSize = 14, fontStyle = FontStyle.Bold });
        GUILayout.Label($"F12: Debug State");
        GUILayout.Label($"F8: Force Save");
        GUILayout.Label($"F9: Show Load Info");
        
        if (GUILayout.Button("Debug Current State"))
        {
            DebugCurrentState();
        }
        
        if (GUILayout.Button("Force Save"))
        {
            ForceSaveInventory();
        }
        
        if (GUILayout.Button("Show Save File"))
        {
            ShowSaveFileInfo();
        }
        
        if (GUILayout.Button("Clear Logs"))
        {
            ClearAllLogs();
        }
        
        GUILayout.EndArea();
    }
}
