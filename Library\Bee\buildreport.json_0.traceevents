{ "pid": 284300, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 284300, "tid": 1, "ts": 1755304216439401, "dur": 29430, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755304216468837, "dur": 234052, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 284300, "tid": 1, "ts": 1755304216702900, "dur": 11043, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218696197, "dur": 1393, "ph": "X", "name": "", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216437624, "dur": 34797, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216472424, "dur": 2208367, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216473290, "dur": 1951, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216475246, "dur": 627, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216475877, "dur": 7257, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216483141, "dur": 245, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216483389, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216483430, "dur": 737, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216484170, "dur": 5339, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216489516, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216489518, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216489565, "dur": 818, "ph": "X", "name": "ProcessMessages 602", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216490386, "dur": 44, "ph": "X", "name": "ReadAsync 602", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216490435, "dur": 272, "ph": "X", "name": "ProcessMessages 171", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216490710, "dur": 3240, "ph": "X", "name": "ReadAsync 171", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216493954, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216493956, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216494034, "dur": 650, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304216494688, "dur": 2160557, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218655255, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218655264, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218655331, "dur": 2181, "ph": "X", "name": "ProcessMessages 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218657517, "dur": 6006, "ph": "X", "name": "ReadAsync 9192", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663532, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663535, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663575, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663577, "dur": 362, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663945, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218663977, "dur": 348, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 284300, "tid": 12884901888, "ts": 1755304218664327, "dur": 15670, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218697646, "dur": 45, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 284300, "tid": 8589934592, "ts": 1755304216434831, "dur": 279161, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755304216713995, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 284300, "tid": 8589934592, "ts": 1755304216714002, "dur": 1253, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218697692, "dur": 104, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 284300, "tid": 4294967296, "ts": 1755304216412770, "dur": 2269026, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755304216416072, "dur": 13144, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755304218681845, "dur": 5417, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755304218684718, "dur": 32, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 284300, "tid": 4294967296, "ts": 1755304218687364, "dur": 23, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218697799, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755304216470988, "dur":17258, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304216488262, "dur":270, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304216488554, "dur":370, "ph":"X", "name": "Tundra",  "args": { "detail":"PrepareNodes" }}
,{ "pid":12345, "tid":0, "ts":1755304216488924, "dur":719, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304216489691, "dur":150, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304216489841, "dur":2173910, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304218663752, "dur":245, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304218664181, "dur":4636, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755304216489740, "dur":108, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755304216489881, "dur":205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755304216490742, "dur":1289, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755304216490091, "dur":3353, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755304216496257, "dur":2160503, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755304216489814, "dur":63, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755304216489878, "dur":2310, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755304216492189, "dur":2171581, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755304216489888, "dur":3505, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755304216493394, "dur":2170384, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755304216490242, "dur":225263, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755304216727202, "dur":317, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1755304216715506, "dur":12017, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755304216727524, "dur":1936228, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755304216490205, "dur":2037, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755304216492243, "dur":2171515, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755304216489909, "dur":3134, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755304216493044, "dur":2170712, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755304216490337, "dur":237189, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755304216727527, "dur":1936243, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755304216490378, "dur":2173390, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755304216490513, "dur":2173235, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755304216490412, "dur":2173335, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755304216490473, "dur":2173277, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755304216490555, "dur":2173191, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755304218679114, "dur":488, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 284300, "tid": 80143, "ts": 1755304218698851, "dur": 2814, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218701824, "dur": 1435, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 284300, "tid": 80143, "ts": 1755304218694752, "dur": 9455, "ph": "X", "name": "Write chrome-trace events", "args": {} },
