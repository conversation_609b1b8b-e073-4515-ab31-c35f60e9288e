using UnityEngine;
using UnityEngine.SceneManagement;
using System.IO;
using System.Collections;
using System.Diagnostics;
using System.Threading;

/// <summary>
/// Comprehensive logging system to track scene transitions and identify hard freeze causes
/// This system logs every step of the loading process with precise timing and system info
/// </summary>
public class SceneTransitionLogger : MonoBehaviour
{
    private static readonly string TRANSITION_LOG_FILE = "scene_transition.log";
    private static SceneTransitionLogger _instance;
    private static bool _isLogging = true;
    
    // Performance monitoring
    private static Stopwatch _transitionStopwatch;
    private static float _lastFrameTime;
    private static int _frameCount;
    private static bool _isTransitioning = false;
    
    // System monitoring
    private static Thread _monitoringThread;
    private static bool _shouldMonitor = false;
    
    private string TransitionLogFilePath => Path.Combine(Application.persistentDataPath, TRANSITION_LOG_FILE);
    
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void Initialize()
    {
        // Create a persistent logger that survives scene transitions
        GameObject loggerObj = new GameObject("SceneTransitionLogger");
        DontDestroyOnLoad(loggerObj);
        _instance = loggerObj.AddComponent<SceneTransitionLogger>();
        
        // Start logging immediately
        LogTransitionEvent("SYSTEM_BOOT", "Scene transition logger initialized");
        
        // Subscribe to scene events
        SceneManager.sceneLoaded += OnSceneLoaded;
        SceneManager.sceneUnloaded += OnSceneUnloaded;
        SceneManager.activeSceneChanged += OnActiveSceneChanged;
    }
    
    private void Awake()
    {
        if (_instance != null && _instance != this)
        {
            Destroy(gameObject);
            return;
        }
        _instance = this;
    }
    
    private void Start()
    {
        LogTransitionEvent("LOGGER_START", $"Logger started in scene: {SceneManager.GetActiveScene().name}");
        StartCoroutine(PerformanceMonitoringCoroutine());
    }
    
    private void Update()
    {
        // Monitor frame time for freeze detection
        if (_isTransitioning)
        {
            float currentFrameTime = Time.unscaledDeltaTime;
            if (currentFrameTime > 0.1f) // Frame took longer than 100ms
            {
                LogTransitionEvent("FRAME_HITCH", $"Long frame detected: {currentFrameTime:F3}s (frame #{_frameCount})");
            }
            _lastFrameTime = currentFrameTime;
            _frameCount++;
        }
    }
    
    private void OnDestroy()
    {
        StopSystemMonitoring();
    }
    
    private void OnApplicationPause(bool pauseStatus)
    {
        LogTransitionEvent("APP_PAUSE", $"Application pause state: {pauseStatus}");
    }
    
    private void OnApplicationFocus(bool hasFocus)
    {
        LogTransitionEvent("APP_FOCUS", $"Application focus state: {hasFocus}");
    }
    
    // ===== PUBLIC API =====
    
    public static void LogSceneTransitionStart(string fromScene, string toScene)
    {
        _isTransitioning = true;
        _transitionStopwatch = Stopwatch.StartNew();
        _frameCount = 0;
        
        LogTransitionEvent("TRANSITION_START", $"Starting transition from '{fromScene}' to '{toScene}'");
        StartSystemMonitoring();
    }
    
    public static void LogSceneTransitionEnd(string sceneName)
    {
        _isTransitioning = false;
        if (_transitionStopwatch != null)
        {
            _transitionStopwatch.Stop();
            LogTransitionEvent("TRANSITION_END", $"Completed transition to '{sceneName}' in {_transitionStopwatch.ElapsedMilliseconds}ms ({_frameCount} frames)");
        }
        StopSystemMonitoring();
    }
    
    public static void LogTransitionStep(string step, string details = "")
    {
        string elapsedInfo = "";
        if (_transitionStopwatch != null && _transitionStopwatch.IsRunning)
        {
            elapsedInfo = $" [+{_transitionStopwatch.ElapsedMilliseconds}ms]";
        }
        
        LogTransitionEvent($"STEP_{step}", details + elapsedInfo);
    }
    
    public static void LogPotentialFreeze(string context)
    {
        LogTransitionEvent("POTENTIAL_FREEZE", $"{context} | Memory: {GetMemoryUsageMB()}MB | Threads: {Process.GetCurrentProcess().Threads.Count}");
    }
    
    // ===== SCENE EVENT HANDLERS =====
    
    private static void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        LogTransitionEvent("SCENE_LOADED", $"Scene '{scene.name}' loaded with mode {mode}");
    }
    
    private static void OnSceneUnloaded(Scene scene)
    {
        LogTransitionEvent("SCENE_UNLOADED", $"Scene '{scene.name}' unloaded");
    }
    
    private static void OnActiveSceneChanged(Scene previousScene, Scene newScene)
    {
        LogTransitionEvent("ACTIVE_SCENE_CHANGED", $"Active scene changed from '{previousScene.name}' to '{newScene.name}'");
    }
    
    // ===== MONITORING COROUTINES =====
    
    private IEnumerator PerformanceMonitoringCoroutine()
    {
        while (true)
        {
            if (_isTransitioning)
            {
                // Log system state every 2 seconds during transitions
                LogTransitionEvent("PERF_CHECK", $"Memory: {GetMemoryUsageMB()}MB | FPS: {1f/Time.unscaledDeltaTime:F1} | Threads: {Process.GetCurrentProcess().Threads.Count}");
            }
            yield return new WaitForSecondsRealtime(2f);
        }
    }
    
    // ===== SYSTEM MONITORING =====
    
    private static void StartSystemMonitoring()
    {
        if (_monitoringThread != null) return;
        
        _shouldMonitor = true;
        _monitoringThread = new Thread(SystemMonitoringThread)
        {
            IsBackground = true,
            Name = "SceneTransitionMonitor"
        };
        _monitoringThread.Start();
    }
    
    private static void StopSystemMonitoring()
    {
        _shouldMonitor = false;
        if (_monitoringThread != null)
        {
            _monitoringThread.Join(1000); // Wait up to 1 second
            _monitoringThread = null;
        }
    }
    
    private static void SystemMonitoringThread()
    {
        var process = Process.GetCurrentProcess();
        var lastCpuTime = process.TotalProcessorTime;
        var lastTime = System.DateTime.UtcNow;
        
        while (_shouldMonitor)
        {
            try
            {
                Thread.Sleep(1000); // Check every second
                
                if (!_shouldMonitor) break;
                
                var currentTime = System.DateTime.UtcNow;
                var currentCpuTime = process.TotalProcessorTime;
                
                var cpuUsage = (currentCpuTime - lastCpuTime).TotalMilliseconds / (currentTime - lastTime).TotalMilliseconds * 100;
                var memoryMB = process.WorkingSet64 / (1024 * 1024);
                
                // Log if CPU usage is very high (potential infinite loop)
                if (cpuUsage > 90)
                {
                    LogTransitionEvent("HIGH_CPU_USAGE", $"CPU: {cpuUsage:F1}% | Memory: {memoryMB}MB | Threads: {process.Threads.Count}");
                }
                
                lastCpuTime = currentCpuTime;
                lastTime = currentTime;
            }
            catch (System.Exception e)
            {
                LogTransitionEvent("MONITOR_ERROR", $"System monitoring error: {e.Message}");
                break;
            }
        }
    }
    
    // ===== UTILITY METHODS =====
    
    private static void LogTransitionEvent(string eventType, string details = "")
    {
        if (!_isLogging) return;
        
        try
        {
            string timestamp = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            string threadInfo = $"T{Thread.CurrentThread.ManagedThreadId}";
            string logEntry = $"[{timestamp}] [{threadInfo}] {eventType}";
            
            if (!string.IsNullOrEmpty(details))
            {
                logEntry += $" | {details}";
            }
            
            // Add stack trace for critical events
            if (eventType.Contains("FREEZE") || eventType.Contains("ERROR") || eventType.Contains("HIGH_CPU"))
            {
                logEntry += $" | STACK: {System.Environment.StackTrace}";
            }
            
            // Write to log file asynchronously
            ThreadPool.QueueUserWorkItem(_ => WriteToLogFile(logEntry));
            
            // Also log to Unity console for immediate visibility
            if (eventType.Contains("ERROR") || eventType.Contains("FREEZE"))
            {
                UnityEngine.Debug.LogError($"[SceneTransition] {logEntry}");
            }
            else if (eventType.Contains("WARNING") || eventType.Contains("HIGH_CPU"))
            {
                UnityEngine.Debug.LogWarning($"[SceneTransition] {logEntry}");
            }
            else
            {
                UnityEngine.Debug.Log($"[SceneTransition] {logEntry}");
            }
        }
        catch (System.Exception e)
        {
            UnityEngine.Debug.LogError($"[SceneTransition] Failed to log event: {e.Message}");
        }
    }
    
    private static void WriteToLogFile(string logEntry)
    {
        try
        {
            string logPath = Path.Combine(Application.persistentDataPath, TRANSITION_LOG_FILE);
            string directory = Path.GetDirectoryName(logPath);
            
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            File.AppendAllText(logPath, logEntry + System.Environment.NewLine);
        }
        catch (System.Exception)
        {
            // Ignore write failures in background thread
        }
    }
    
    private static long GetMemoryUsageMB()
    {
        try
        {
            return Process.GetCurrentProcess().WorkingSet64 / (1024 * 1024);
        }
        catch
        {
            return -1;
        }
    }
    
    // ===== PUBLIC UTILITY METHODS =====
    
    public static void EnableLogging(bool enable)
    {
        _isLogging = enable;
        LogTransitionEvent("LOGGING_STATE", $"Logging {(enable ? "enabled" : "disabled")}");
    }
    
    public static void ClearLogFile()
    {
        try
        {
            string logPath = Path.Combine(Application.persistentDataPath, TRANSITION_LOG_FILE);
            if (File.Exists(logPath))
            {
                File.Delete(logPath);
                LogTransitionEvent("LOG_CLEARED", "Transition log file cleared");
            }
        }
        catch (System.Exception e)
        {
            LogTransitionEvent("LOG_CLEAR_ERROR", $"Failed to clear log: {e.Message}");
        }
    }
}
