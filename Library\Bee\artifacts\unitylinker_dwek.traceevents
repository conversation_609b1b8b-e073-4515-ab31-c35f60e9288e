{ "pid": 300860, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 300860, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 300860, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 300860, "tid": 1, "ts": 1755304253473364, "dur": 1540595, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 300860, "tid": 1, "ts": 1755304253475423, "dur": 301168, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304253735764, "dur": 40338, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304253818446, "dur": 16211, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304253835692, "dur": 128049, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304253963777, "dur": 831291, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304254795084, "dur": 211126, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304255011544, "dur": 2261, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304255013961, "dur": 420, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304255023953, "dur": 2080, "ph": "X", "name": "", "args": {} },
{ "pid": 300860, "tid": 1, "ts": 1755304255023343, "dur": 2963, "ph": "X", "name": "Write chrome-trace events", "args": {} },
