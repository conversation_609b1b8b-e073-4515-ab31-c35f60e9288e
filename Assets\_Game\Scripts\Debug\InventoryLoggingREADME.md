# Inventory State Logging System

## Overview
This system continuously monitors and logs changes to your equipment and inventory to help track down the bug that wipes your inventory. It creates a detailed log file that records every equipment change with timestamps and context.

## Log File Location
The log file is saved at: `%USERPROFILE%/AppData/LocalLow/[CompanyName]/[GameName]/inventory_state.log`

You can find the exact path by:
1. Adding the `InventoryLoggerTest` component to any GameObject
2. Right-clicking the component and selecting "Open Log File Location"

## What Gets Logged
- **Equipment changes**: When items are equipped/unequipped
- **Container operations**: Adding/removing/moving items in bags
- **Game state changes**: Game start, loading, manager destruction
- **Periodic checks**: Every 10 seconds to detect unexpected wipes
- **Critical events**: When inventory suddenly becomes empty

## Log Entry Format
```
[2024-01-15 14:30:25.123] ACTION: EQUIP_ITEM | CONTEXT: Equipped Backpack to BagSlot | STATE: EQUIPPED:[BagSlot:Backpack(0items)] TOTAL_ITEMS:0
```

Each entry contains:
- **Timestamp**: Precise time when the change occurred
- **Action**: What operation was performed
- **Context**: Additional details about the operation
- **State**: Current equipment and item counts
- **Stack Trace**: For critical events like wipes

## Key Actions to Watch For
- `EQUIP_ITEM` / `UNEQUIP_ITEM`: Normal equipment changes
- `ADD_ITEM_TO_CONTAINER` / `REMOVE_ITEM_FROM_CONTAINER`: Inventory operations
- `PERIODIC_WIPE_DETECTED`: Automatic detection of inventory wipes
- `EQUIPMENT_SLOTS_REINITIALIZED`: Potential wipe during initialization

## How to Use
1. **Automatic Logging**: The system starts automatically when the game loads
2. **Manual Testing**: Add `InventoryLoggerTest` component and press F9 or use context menu
3. **Monitor for Wipes**: Look for entries where STATE changes from having items to `EQUIPPED:[NONE]`

## Finding the Bug
When your inventory gets wiped, look for:
1. The last normal inventory operation before the wipe
2. Any `PERIODIC_WIPE_DETECTED` entries
3. Stack traces that show what code caused the wipe
4. Patterns in timing (does it happen during specific actions?)

## Performance Impact
- Minimal: Logging runs on background threads
- Only logs when state actually changes
- Periodic checks every 10 seconds
- Can be disabled by setting `enableInventoryLogging = false` in EquipmentManager

## Troubleshooting
- If no log file appears, check that the EquipmentManager exists in your scene
- Use the test component to verify logging is working
- Check Unity console for any logging errors
- Log file is created in Unity's persistent data path

## Example Investigation
If you see this sequence:
```
[14:30:25] ACTION: ADD_ITEM_TO_CONTAINER | CONTEXT: Added 1x Sword to BagSlot
[14:30:45] ACTION: PERIODIC_WIPE_DETECTED | CONTEXT: Periodic check detected inventory wipe
```

This tells you the inventory was wiped sometime between 14:30:25 and 14:30:45, and no logged action caused it, suggesting the bug is in unmonitored code.
