{"totalVariantsIn": 320933, "totalVariantsOut": 5770, "shaders": [{"inputVariants": 6, "outputVariants": 6, "name": "Shader Graphs/PhysicallyBasedSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 10.4686}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0718}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBR Sky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0694}]}]}, {"inputVariants": 434, "outputVariants": 122, "name": "Shader Graphs/Water", "pipelines": [{"inputVariants": 434, "outputVariants": 122, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.2156}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3619}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5618000000000001}, {"inputVariants": 72, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.36100000000000004}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Hull)", "stripTimeMs": 0.4082}, {"inputVariants": 24, "outputVariants": 12, "variantName": "WaterGBufferTessellation (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Domain)", "stripTimeMs": 0.5459}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0653}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049100000000000005}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5081}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMask (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.32170000000000004}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3387}, {"inputVariants": 48, "outputVariants": 12, "variantName": "WaterMaskLowRes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.47100000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5928}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0818}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0789}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08270000000000001}]}]}, {"inputVariants": 37, "outputVariants": 16, "name": "Shader Graphs/SolidColor", "pipelines": [{"inputVariants": 37, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7034}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0577}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.112}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07740000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13770000000000002}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1068}, {"inputVariants": 4, "outputVariants": 2, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1148}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0152}, {"inputVariants": 2, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0756}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0877}, {"inputVariants": 2, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0332}, {"inputVariants": 2, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.033800000000000004}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "HDRP/DefaultFogVolume", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.6447}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12840000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.095}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0758}, {"inputVariants": 2, "outputVariants": 2, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14170000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Shader Graphs/Sample Water Decal", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0799}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Deformation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0873}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0795}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Foam (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0903}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Skybox/Cubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 144, "outputVariants": 144, "name": "Hidden/HDRP/Sky/CloudLayer", "pipelines": [{"inputVariants": 144, "outputVariants": 144, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.1025}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3806}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5159}, {"inputVariants": 48, "outputVariants": 48, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.4758}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Waveform", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0146}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/DLSSBiasColorMask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.021}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/WaterCaustics", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7068}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0385}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ApplyDistortion", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.8466000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0415}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CameraMotionVectors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.8718}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CompositeUI", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.1991000000000005}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1602}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/CanvasBackground", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "GridBackground (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.8806}, {"inputVariants": 2, "outputVariants": 2, "variantName": "GridBackground (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057300000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/VoxelizeShader", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6651}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0335}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0178}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/HDRP/DebugExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/GGXConvolve", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.554600000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_GGXDisneyDiffuse", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.1827000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0308}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/CoreSRP/CoreCopy", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6924}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040400000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0284}, {"inputVariants": 2, "outputVariants": 2, "variantName": "CopyMS (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/UpsampleTransparent", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.1836}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0341}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0261}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031400000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/Sky/GradientSky", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2527}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.030500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0268}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CombineLighting", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.7769}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ColorPyramidPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.3735}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_<PERSON>ner", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.4007000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0256}]}]}, {"inputVariants": 44, "outputVariants": 24, "name": "Hidden/HDRP/OpaqueAtmosphericScattering", "pipelines": [{"inputVariants": 44, "outputVariants": 24, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.1381000000000006}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0403}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0342}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MSAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0545}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0568}, {"inputVariants": 16, "outputVariants": 8, "variantName": "Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1593}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}, {"inputVariants": 16, "outputVariants": 8, "variantName": "MSAA + Polychromatic Alpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.129}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearBlack", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3998}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VFX/Empty", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.321000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2234}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialLoading", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3943}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049100000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/ProbeVolumeFragmentationDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.2049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/Core/ProbeVolumeDebug", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/preIntegratedFGD_CharlieFabricLambert", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4116}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033800000000000004}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CopyStencilBuffer", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3539}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 - Copy stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.033100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 - Write 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 - Export HTILE for stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0303}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 - Initialize Stencil UAV copy with 1 if value different from stencilRef to output (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 4 - Update Stencil UAV copy with Stencil Ref (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0236}]}]}, {"inputVariants": 1284, "outputVariants": 1284, "name": "Hidden/HDRP/TemporalAA", "pipelines": [{"inputVariants": 1284, "outputVariants": 1284, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.9295}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.4509}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Excluded From TAA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.9266}, {"inputVariants": 1, "outputVariants": 1, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.047400000000000005}, {"inputVariants": 320, "outputVariants": 320, "variantName": "TAAU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.7977000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 320, "outputVariants": 320, "variantName": "Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.4881}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/CopyDepthBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6927}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy Depth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0332}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewMaterialGBuffer", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0057}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/XROcclusionMesh", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4926}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0553}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/IntegrateHDRI", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2866}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0328}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/ChromaKeying", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.1014}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ChromaKeying (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028800000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Renderers/Thickness", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2969}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessOpaque (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.05}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ComputeThicknessTransparent (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044700000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader/AlphaInjection", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.4334}, {"inputVariants": 1, "outputVariants": 1, "variantName": "AlphaInjection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/DebugVTBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/CharlieConvolve", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0038}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/CubeToPano", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.4443}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025400000000000002}]}]}, {"inputVariants": 769, "outputVariants": 257, "name": "Hidden/HDRP/FinalPass", "pipelines": [{"inputVariants": 769, "outputVariants": 257, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.5595}, {"inputVariants": 768, "outputVariants": 256, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 6.673900000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/VrsVisualization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.0953}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VrsVisualization (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0366}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.2011}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 3, "outputVariants": 3, "name": "Hidden/HDRP/DownsampleDepth", "pipelines": [{"inputVariants": 3, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.2462}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054200000000000005}]}]}, {"inputVariants": 288790, "outputVariants": 486, "name": "HDRP/Lit", "pipelines": [{"inputVariants": 288790, "outputVariants": 486, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 120, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.5232}, {"inputVariants": 5184, "outputVariants": 240, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 17.7743}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1764}, {"inputVariants": 12, "outputVariants": 12, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.21580000000000002}, {"inputVariants": 144, "outputVariants": 24, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7474000000000001}, {"inputVariants": 360, "outputVariants": 30, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.1837}, {"inputVariants": 72, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5468000000000001}, {"inputVariants": 144, "outputVariants": 12, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.606}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1651}, {"inputVariants": 36, "outputVariants": 6, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2094}, {"inputVariants": 144, "outputVariants": 12, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.33}, {"inputVariants": 93312, "outputVariants": 24, "variantName": "TransparentBackface (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 142.1561}, {"inputVariants": 144, "outputVariants": 12, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.2451}, {"inputVariants": 186624, "outputVariants": 24, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 355.94030000000004}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1748}, {"inputVariants": 18, "outputVariants": 6, "variantName": "TransparentDepthPostpass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1738}, {"inputVariants": 4, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0172}, {"inputVariants": 12, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.030100000000000002}, {"inputVariants": 864, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 3.2694}, {"inputVariants": 432, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.8565}, {"inputVariants": 864, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 2.9066}, {"inputVariants": 12, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.21610000000000001}, {"inputVariants": 72, "outputVariants": 0, "variantName": "SubSurfaceDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3855}, {"inputVariants": 6, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3839}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/DepthValues", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.2584}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1081}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0454}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/CircularProgress", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6681}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0716}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/MaterialError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6787}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0531}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/PostProcessing/Debug/Vectorscope", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.013300000000000001}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/CustomPassUtils", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3207}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Copy (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0345}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CopyDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0177}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0273}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Downsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0247}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "HorizontalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VerticalBlur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0217}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugColorPicker", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLocalVolumetricFogAtlas", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/HDRP/LensFlareDataDriven", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.8302000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0702}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0534}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.057300000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0708}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0784}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/FallbackError", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.436800000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039900000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/CompositeLines", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.6161}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeAll (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0577}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0241}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "CompositeDepthMovecOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0194}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugLightVolumes", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/ClearStencilBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.4514000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038200000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ScriptableRenderPipeline/DebugDisplayHDShadowMap", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.0924000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RegularShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0477}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0308}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VarianceShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/PostProcessing/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3399}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Edge detection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0539}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Blend Weights Calculation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0523}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Neighborhood Blending (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0425}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Hidden/HDRP/LensFlareScreenSpace", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.9329}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpac Prefilter (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0234}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Downsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Upsample (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0262}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Composition (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareScreenSpace Write to BloomTexture (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0253}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/SVSquare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.5572}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033100000000000004}]}]}, {"inputVariants": 3, "outputVariants": 0, "name": "Hidden/HDRP/DebugHDR", "pipelines": [{"inputVariants": 3, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0092}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 868, "outputVariants": 28, "name": "Hidden/HDRP/Sky/HDRISky", "pipelines": [{"inputVariants": 868, "outputVariants": 28, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.976}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragBaking (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3733}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0471}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRender (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6721}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRenderBackplate (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3934}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0502}, {"inputVariants": 216, "outputVariants": 6, "variantName": "FragRenderBackplateDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3643}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/CustomClear", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 10.229000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearColorAndStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0298}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawTextureAndClearStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/HDRP/Sky/PbrSky", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.810300000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky Cubemap (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0417}, {"inputVariants": 1, "outputVariants": 1, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0412}, {"inputVariants": 2, "outputVariants": 2, "variantName": "PBRSky (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0313}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_CookTorrance", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.389}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/ColorSwatch", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.498}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0183}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/BlitColorAndDepth", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6061000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07050000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0892}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ColorAndDepth (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061500000000000006}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOccluder", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.3074}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DebugOccluder (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.050300000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/App UI/LinearProgress", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.5829}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0653}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/HDRP/WaterExclusion", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.5151}, {"inputVariants": 2, "outputVariants": 2, "variantName": "StencilTag (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeSamplingDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7651}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0684}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/ScreenSpaceShadows", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0154}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugDisplayLatlong", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0074}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/CoreResources/FilterAreaLightCookies", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.9855}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0432}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0236}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0264}]}]}, {"inputVariants": 86, "outputVariants": 36, "name": "HDRP/Unlit", "pipelines": [{"inputVariants": 86, "outputVariants": 36, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.39630000000000004}, {"inputVariants": 12, "outputVariants": 6, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1149}, {"inputVariants": 3, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0787}, {"inputVariants": 12, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1422}, {"inputVariants": 6, "outputVariants": 3, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0922}, {"inputVariants": 12, "outputVariants": 6, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.131}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08270000000000001}, {"inputVariants": 6, "outputVariants": 6, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.109}, {"inputVariants": 3, "outputVariants": 0, "variantName": "DistortionVectors (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.009600000000000001}, {"inputVariants": 6, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.010100000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0407}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0427}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.046900000000000004}, {"inputVariants": 2, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.036000000000000004}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.033800000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/ColorWheel", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.2083}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0309}]}]}, {"inputVariants": 16, "outputVariants": 0, "name": "Hidden/HDRP/DebugViewTiles", "pipelines": [{"inputVariants": 16, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 16, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0261}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowBlit", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.0324}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BlitShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0386}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/HDRP/CustomPassRenderersUtils", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.7236}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0625}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06280000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.063}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NormalToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0483}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TangentToColorPass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048100000000000004}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "Hidden/HDRP/DebugBlitQuad", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0111}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.006200000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/HDRP/WaterDecal", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.8956}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DeformationDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0398}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MaskDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0188}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LargeCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "RipplesCurrentDecal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0267}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FoamAttenuation (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.019700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCubemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.463}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0379}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/HDRP/PreIntegratedFGD_Ward", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7773}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0459}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/DebugOcclusionTest", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0374}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0352}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/SRP/BlitCubeTextureFace", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0768}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0381}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shader Graph/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.7625}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0328}]}]}, {"inputVariants": 4, "outputVariants": 3, "name": "Hidden/Core/ProbeVolumeOffsetDebug", "pipelines": [{"inputVariants": 4, "outputVariants": 3, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.0319}, {"inputVariants": 3, "outputVariants": 2, "variantName": "ForwardOnly (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07690000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/ColorResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6611}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA1X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA2X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA4X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.024}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "MSAA8X (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0235}]}]}, {"inputVariants": 22, "outputVariants": 22, "name": "Hidden/App UI/Box", "pipelines": [{"inputVariants": 22, "outputVariants": 22, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.6725}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Clear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0334}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BoxShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07100000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0456}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundColor (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0253}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BackgroundImage (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0184}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0439}, {"inputVariants": 2, "outputVariants": 2, "variantName": "InsetBoxShadows (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0611}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Border (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0699}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Outline (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0489}]}]}, {"inputVariants": 200, "outputVariants": 200, "name": "Hidden/HDRP/Blit", "pipelines": [{"inputVariants": 200, "outputVariants": 200, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7217}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Nearest (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0688}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.094}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Bilinear (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07690000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0823}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.097}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06770000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuad (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07150000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07400000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0716}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0747}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07640000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0582}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0766}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0736}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0751}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0664}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0859}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0834}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0944}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0521}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlend (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0649}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0551}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052500000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.050800000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendRepeat (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.060000000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadPaddingAlphaBlendOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0526}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedral (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0646}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0716}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10500000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07830000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0729}, {"inputVariants": 4, "outputVariants": 4, "variantName": "CubeToOctahedralRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0582}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.11230000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadLuminance (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061700000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06910000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadAlpha (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09580000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.231}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearQuadRed (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1825}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.13670000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "NearestCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.132}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1486}, {"inputVariants": 8, "outputVariants": 8, "variantName": "BilinearCubeToOctahedralPadding (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12380000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ScriptableRenderPipeline/ShadowClear", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4836}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ClearShadow (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0419}]}]}, {"inputVariants": 5, "outputVariants": 3, "name": "Hidden/HDRP/XRMirrorView", "pipelines": [{"inputVariants": 5, "outputVariants": 3, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.1343000000000005}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.052500000000000005}]}]}, {"inputVariants": 1, "outputVariants": 0, "name": "Hidden/HDRP/DebugFullScreen", "pipelines": [{"inputVariants": 1, "outputVariants": 0, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0111}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/HDRP/MotionVecResolve", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0397}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0329}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 3, "outputVariants": 2, "name": "Hidden/HDRP/Material/Decal/DecalNormalBuffer", "pipelines": [{"inputVariants": 3, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.0601}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040400000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/App UI/Mask", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3612}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 73, "outputVariants": 28, "name": "Shader Graphs/TIPS_Mesh 2", "pipelines": [{"inputVariants": 73, "outputVariants": 28, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.7100000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.15610000000000002}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1076}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0908}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.15910000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1695}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.20320000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.16}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0182}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1082}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1436}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0823}, {"inputVariants": 4, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0816}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.043500000000000004}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.07980000000000001}]}]}, {"inputVariants": 59, "outputVariants": 20, "name": "Shader Graphs/TIPS_Mesh", "pipelines": [{"inputVariants": 59, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5306000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0742}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08270000000000001}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11670000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0964}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.10400000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.111}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13520000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0132}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1044}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.12350000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0729}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0427}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0392}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0733}]}]}, {"inputVariants": 22211, "outputVariants": 56, "name": "Samples/SamplesLit_Inter", "pipelines": [{"inputVariants": 22211, "outputVariants": 56, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0026000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.18730000000000002}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1993}, {"inputVariants": 32, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2147}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08950000000000001}, {"inputVariants": 4, "outputVariants": 2, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.11040000000000001}, {"inputVariants": 4, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0187}, {"inputVariants": 8, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3058}, {"inputVariants": 48, "outputVariants": 4, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.46530000000000005}, {"inputVariants": 16, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3864}, {"inputVariants": 576, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 2.2272000000000003}, {"inputVariants": 16, "outputVariants": 4, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3916}, {"inputVariants": 20736, "outputVariants": 8, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 48.241600000000005}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0164}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.5850000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1862}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3725}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3898000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0351}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.4562}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0004}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Unlit/Texture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.028300000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/ReduceExpBias", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7115}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0396}]}]}, {"inputVariants": 86, "outputVariants": 86, "name": "Hidden/InferenceEngine/Activation", "pipelines": [{"inputVariants": 86, "outputVariants": 86, "pipeline": "", "variants": [{"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3491}, {"inputVariants": 43, "outputVariants": 43, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.7315}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Dense", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.7625}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09380000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/Pad", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.9623}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14700000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/Mat<PERSON>ul", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.3715}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Transpose", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6738}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054200000000000005}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/InferenceEngine/ActivationInt", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.941}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1148}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Trilu", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3843000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0422}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/InstanceNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.7702}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0323}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Tile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0713}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040600000000000004}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/TextureConversion/TensorToTexture", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4404}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.22}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/TextureTensorDataDownload", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6962}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0825}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/CumSum", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.8341}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1812}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/LayerNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.76}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0539}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/IsInfNaN", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2734000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0592}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/GatherElements", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4985}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0689}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/TextureConversion/TextureToTensor", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.8994}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.14780000000000001}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/DepthwiseConv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.1808000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.23720000000000002}]}]}, {"inputVariants": 1296, "outputVariants": 1296, "name": "Hidden/InferenceEngine/ConvTranspose", "pipelines": [{"inputVariants": 1296, "outputVariants": 1296, "pipeline": "", "variants": [{"inputVariants": 648, "outputVariants": 648, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 13.9092}, {"inputVariants": 648, "outputVariants": 648, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 8.475900000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/DepthToSpace", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.0753}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07250000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "FullScreen/test", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.6414}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0339}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/SpaceToDepth", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.904}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046900000000000004}]}]}, {"inputVariants": 40, "outputVariants": 40, "name": "Hidden/InferenceEngine/ScatterND", "pipelines": [{"inputVariants": 40, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3693}, {"inputVariants": 20, "outputVariants": 20, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2968}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.8019}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0855}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Reshape", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3505}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09720000000000001}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/Conv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.4909}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2046}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "Hidden/InferenceEngine/GridSample", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.7936000000000005}, {"inputVariants": 36, "outputVariants": 36, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.6031000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/TextureConversion/ComputeBufferToTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.5604000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053500000000000006}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Gather", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.2974000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0517}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Cast", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6268}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0563}]}]}, {"inputVariants": 70, "outputVariants": 70, "name": "Hidden/InferenceEngine/Broadcast", "pipelines": [{"inputVariants": 70, "outputVariants": 70, "pipeline": "", "variants": [{"inputVariants": 35, "outputVariants": 35, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3609}, {"inputVariants": 35, "outputVariants": 35, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3456}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/BatchNormalization", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.349600000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0396}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Gemm", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.6117}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0906}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/EdgePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.5522}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Edges (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0333}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/TextureTensorDataUpload", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2969}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "FullScreen/TIPS", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.348}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Compositing (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0227}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Hidden/InferenceEngine/Reduce", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "", "variants": [{"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.792000000000001}, {"inputVariants": 18, "outputVariants": 18, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.24930000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Softmax", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.6164000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/RoiAlign", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.8607000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09340000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Random", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.5949}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0709}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/HardmaxEnd", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.1923}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042800000000000005}]}]}, {"inputVariants": 80, "outputVariants": 80, "name": "Hidden/InferenceEngine/ScatterElements", "pipelines": [{"inputVariants": 80, "outputVariants": 80, "pipeline": "", "variants": [{"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4655000000000005}, {"inputVariants": 40, "outputVariants": 40, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.46780000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/GatherND", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.3761}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044700000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ConstantOfShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.895300000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0466}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Where", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0965}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0646}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Copy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.099600000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0516}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/RMSNormalizationTail", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.026400000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0342}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/VertexPicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.869000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertices (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0419}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/InferenceEngine/Upsample", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4145}, {"inputVariants": 9, "outputVariants": 9, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.12090000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/SliceSet", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.761500000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1131}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Expand", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4914000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/GlobalPool", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.5055000000000005}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0942}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/FacePicker", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.5496}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Base (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3007}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0651}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ProBuilder/HideVertices", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0277}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038400000000000004}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Hidden/InferenceEngine/GroupedConv", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 7.388800000000001}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.22970000000000002}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Split", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.9223}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0789}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/Range", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.0129}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046400000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/OneHot", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0314000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0634}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/InferenceEngine/Resize1D", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.4581}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08610000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/LocalPool", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0414}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0694}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ScalarMad", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.0224}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040100000000000004}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Hidden/InferenceEngine/ReduceIndices", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 6.662}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1572}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/InferenceEngine/Slice", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.3494}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0639}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/ScaleBias", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.433800000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0483}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/InferenceEngine/LayoutSwitchBlockedAxis", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 4.4449000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06910000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/InferenceEngine/TopP", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.2835}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.1024}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.13290000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Legacy Shaders/Particles/Alpha Blended", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0746}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.061700000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Sprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.049600000000000005}]}]}, {"inputVariants": 1108, "outputVariants": 32, "name": "ProBuilder6/Standard Vertex Color", "pipelines": [{"inputVariants": 1108, "outputVariants": 32, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.8791}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0755}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1449}, {"inputVariants": 16, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1767}, {"inputVariants": 2, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.011600000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0086}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.156}, {"inputVariants": 24, "outputVariants": 2, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.2083}, {"inputVariants": 8, "outputVariants": 4, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.15480000000000002}, {"inputVariants": 288, "outputVariants": 16, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 1.0588}, {"inputVariants": 8, "outputVariants": 0, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3434000000000001}, {"inputVariants": 2, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0845}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3123}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3421}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0456}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.6831}]}, {"inputVariants": 0, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Forward (ForwardBase) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.00030000000000000003}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn ForwardAdd (ForwardAdd) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "BuiltIn Deferred (Deferred) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}, {"inputVariants": 0, "outputVariants": 0, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0002}]}]}, {"inputVariants": 1783, "outputVariants": 78, "name": "Shader Graphs/BakeryVolumeGraph", "pipelines": [{"inputVariants": 1783, "outputVariants": 78, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 2.3402000000000003}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0881}, {"inputVariants": 6, "outputVariants": 3, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.169}, {"inputVariants": 48, "outputVariants": 6, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.5536}, {"inputVariants": 3, "outputVariants": 0, "variantName": "TransparentDepthPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0199}, {"inputVariants": 3, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0085}, {"inputVariants": 6, "outputVariants": 3, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2499}, {"inputVariants": 72, "outputVariants": 6, "variantName": "DepthOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.63}, {"inputVariants": 12, "outputVariants": 6, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2597}, {"inputVariants": 864, "outputVariants": 48, "variantName": "GBuffer (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 4.1817}, {"inputVariants": 12, "outputVariants": 0, "variantName": "Forward (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.26080000000000003}, {"inputVariants": 2, "outputVariants": 0, "variantName": "RayTracingPrepass (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0056}, {"inputVariants": 288, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.3452000000000002}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.1529}, {"inputVariants": 24, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.3019}, {"inputVariants": 288, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 1.5286000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0476}, {"inputVariants": 144, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.5834}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "HDRPSamples/LocalClouds", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.0494}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FogVolumeVoxelize (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0815}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.08170000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "ShaderGraphPreview (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0849}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0476}, {"inputVariants": 1, "outputVariants": 1, "variantName": "VolumetricFogVFXOverdrawDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.060700000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "CustomPass_SG/Outline", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 1.6465}, {"inputVariants": 1, "outputVariants": 1, "variantName": "DrawProcedural (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0446}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0315}]}]}, {"inputVariants": 59, "outputVariants": 20, "name": "Shader Graphs/Glitch_SG", "pipelines": [{"inputVariants": 59, "outputVariants": 20, "pipeline": "HDRenderPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.5345}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0891}, {"inputVariants": 4, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0911}, {"inputVariants": 8, "outputVariants": 2, "variantName": "MotionVectors (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0631}, {"inputVariants": 2, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0746}, {"inputVariants": 4, "outputVariants": 2, "variantName": "DepthForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09430000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1307}, {"inputVariants": 8, "outputVariants": 4, "variantName": "ForwardOnly (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1259}, {"inputVariants": 2, "outputVariants": 0, "variantName": "FullScreenDebug (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0125}, {"inputVariants": 4, "outputVariants": 0, "variantName": "IndirectDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0758}, {"inputVariants": 4, "outputVariants": 0, "variantName": "VisibilityDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0819}, {"inputVariants": 4, "outputVariants": 0, "variantName": "ForwardDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0611}, {"inputVariants": 2, "outputVariants": 0, "variantName": "GBufferDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0302}, {"inputVariants": 1, "outputVariants": 0, "variantName": "DebugDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0284}, {"inputVariants": 4, "outputVariants": 0, "variantName": "PathTracingDXR (ScriptableRenderPipeline) (SubShader: 1) (ShaderType: Surface)", "stripTimeMs": 0.0494}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/BillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0269}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Bark Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0334}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0454}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Soft Occlusion Leaves Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0395}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CameraFacingBillboardTree", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0408}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0424}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Albedo Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047400000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Nature/Tree Creator Normal Rendertex", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0419}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0361}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/Splatmap/Standard-BaseGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0261}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0252}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.029500000000000002}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Hidden/TerrainEngine/PaintHeight", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027600000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Raise/Lower Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0253}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stamp Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0246}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0279}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.029}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Smooth Heights (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0337}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.026500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Texture (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Paint Holes (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.026000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/HeightBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0257}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/GenerateNormalmap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0236}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0302}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/TerrainEngine/TerrainLayerUtils", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0502}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Get Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0485}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Set Terrain Layer Channel (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0386}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Blit Copy Highest Mip (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.022500000000000003}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TerrainEngine/CrossBlendNeighbors", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0352}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0258}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Mobile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.3785}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Graphy/Graph Standard", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.45280000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0597}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0853}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0627}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07980000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0688}]}]}, {"inputVariants": 46, "outputVariants": 46, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 46, "outputVariants": 46, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.19010000000000002}, {"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1091}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0796}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.08270000000000001}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1091}, {"inputVariants": 8, "outputVariants": 8, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.15330000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0509}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.049600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0449}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0523}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0349}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.033800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.031100000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0395}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.049800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.039900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.0405}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0388}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.047900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.049300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0398}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0876}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.09340000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0985}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.083}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.1111}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0887}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0738}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0771}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053700000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0579}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0371}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039900000000000005}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.2576}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.3498}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0485}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0603}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0579}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0716}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0577}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.054200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0297}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0284}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0252}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0455}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0409}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.05}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0386}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0426}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0563}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0563}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061500000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042800000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06280000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0577}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0359}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0651}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0224}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0257}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0228}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0263}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0287}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0201}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0205}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0221}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.020900000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0211}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.02}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0287}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0252}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0337}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0403}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.040600000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023100000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.027700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0444}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1119}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07440000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0745}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0745}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.108}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0848}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0707}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0741}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0477}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0386}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0307}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0436}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.027600000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0286}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0347}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0396}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0275}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.023700000000000002}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0257}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0352}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0425}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0424}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0352}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.0251}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0413}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0468}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0738}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.029500000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0442}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0281}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0373}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0409}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0422}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.050800000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031400000000000004}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0487}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0456}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.051800000000000006}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0663}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0579}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0712}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0541}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.058}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0685}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.066}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0665}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0551}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0553}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0682}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0514}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0687}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054900000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0541}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0347}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0256}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0414}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.031200000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Outline", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Custom Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0228}]}]}]}