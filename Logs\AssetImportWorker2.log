[Licensing::Module] Trying to connect to existing licensing client channel...
Built from '6000.2/respin/6000.2.0f1-517f89d850d1' branch; Version is '6000.2.0f1 (eed1c594c913) revision 15651269'; Using compiler version '194234433'; Build Type 'Release'
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias" at "2025-08-15T20:32:23.9241437Z"
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-08-15T20:32:23Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.2.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
55728
-licensingIpc
LicenseClient-ilias
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [265984]  Target information:

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1256253388 [EditorId] 1256253388 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [265984] Host joined multi-casting on [***********:54997]...
Player connection [265984] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 18644, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Error: HandshakeResponse reported an error:
	ResponseCode: 505
	ResponseStatus: Unsupported protocol version '1.17.1'.
[Licensing::Module] Error: Failed to handshake to channel: "LicenseClient-ilias"
[Licensing::IpcConnector] LicenseClient-ilias channel disconnected successfully.
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0" at "2025-08-15T20:32:24.0238884Z"
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 290512, path: "C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/Licensing/Client/Unity.Licensing.Client.exe")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.1+ae67fbc
  Session Id:              1c31e312aba445c18a1f40744c88aefa
  Correlation Id:          5f91f71ccf6c9acad3d19ab3e1610c0c
  External correlation Id: 2970768556059990955
  Machine Id:              /GaE4A2aXaDfTh/RbExLPR8H29M=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-ilias-6000.2.0" (connect: 0.00s, validation: 0.01s, handshake: 0.02s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-ilias-6000.2.0-notifications" at "2025-08-15T20:32:24.0464662Z"
[Licensing::Module] Licensing Background thread has ended after 0.12s
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Active build profile path: `Assets/Settings/Build Profiles/New Windows Profile.asset`
Got setting from build profile YAML, name `m_BuildTarget`, value `19`
Got setting from build profile YAML, name `m_Subtarget`, value `2`
Refreshing native plugins compatible for Editor in 384.18 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 1.30 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.2.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.8088
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.2.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56604
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.2.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.005778 seconds.
- Loaded All Assemblies, in  0.549 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.371 seconds
Domain Reload Profiling: 909ms
	BeginReloadAssembly (236ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (221ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (169ms)
			TypeCache.Refresh (167ms)
				TypeCache.ScanAssembly (153ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (372ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (315ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (59ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (128ms)
			ProcessInitializeOnLoadMethodAttributes (52ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Got setting from build profile YAML, name `m_Development`, value `0`
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.072 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.62 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.912 seconds
Domain Reload Profiling: 3944ms
	BeginReloadAssembly (346ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (40ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (1554ms)
		LoadAssemblies (1111ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (603ms)
			TypeCache.Refresh (485ms)
				TypeCache.ScanAssembly (461ms)
			BuildScriptInfoCaches (96ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1913ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1712ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (342ms)
			ProcessInitializeOnLoadAttributes (616ms)
			ProcessInitializeOnLoadMethodAttributes (716ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Refreshing native plugins compatible for Editor in 29.93 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 64 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10570 unused Assets / (19.5 MB). Loaded Objects now: 11636.
Memory consumption went from 350.6 MB to 331.1 MB.
Total: 24.281400 ms (FindLiveObjects: 2.020800 ms CreateObjectMapping: 2.015700 ms MarkObjects: 10.953200 ms  DeleteObjects: 9.281800 ms)

========================================================================
Received Import Request.
  Time since last request: 509845.554355 seconds.
  path: Assets/_Game/Scripts/unity-performance-diagnostic.cs
  artifactKey: Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-performance-diagnostic.cs using Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '06a00a7c76348d3cbbac7163cbde259d') in 0.0039151 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.271899 seconds.
  path: Assets/_Game/Scripts/unity-startup-manager.cs
  artifactKey: Guid(58f19517d2905e6499ccd75a542f8b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-startup-manager.cs using Guid(58f19517d2905e6499ccd75a542f8b6c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9bb9a6fe1c2d496a5c8792333a129feb') in 0.0006235 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.301 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.16 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.586 seconds
Domain Reload Profiling: 3856ms
	BeginReloadAssembly (614ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1535ms)
		LoadAssemblies (1303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (500ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (457ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1586ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1279ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (366ms)
			ProcessInitializeOnLoadAttributes (598ms)
			ProcessInitializeOnLoadMethodAttributes (274ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 24.35 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10567 unused Assets / (19.8 MB). Loaded Objects now: 11664.
Memory consumption went from 359.8 MB to 340.0 MB.
Total: 15.103900 ms (FindLiveObjects: 0.933100 ms CreateObjectMapping: 0.539000 ms MarkObjects: 8.442200 ms  DeleteObjects: 5.188700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.512 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.84 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.749 seconds
Domain Reload Profiling: 6229ms
	BeginReloadAssembly (883ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (66ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (81ms)
	LoadAllAssembliesAndSetupDomain (3429ms)
		LoadAssemblies (2920ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (946ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (897ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1750ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (546ms)
			ProcessInitializeOnLoadAttributes (554ms)
			ProcessInitializeOnLoadMethodAttributes (267ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 20.17 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10565 unused Assets / (19.8 MB). Loaded Objects now: 11665.
Memory consumption went from 359.9 MB to 340.0 MB.
Total: 15.721100 ms (FindLiveObjects: 1.530800 ms CreateObjectMapping: 1.364800 ms MarkObjects: 6.609800 ms  DeleteObjects: 6.214400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 554.493167 seconds.
  path: Assets/_Game/Models/Area-1/Cube_006.fbx
  artifactKey: Guid(1959c39d96dc4ae43998a694bf51860f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_006.fbx using Guid(1959c39d96dc4ae43998a694bf51860f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2450af67d79492c7c9dbd223f90bf3ec') in 1.8274125 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Cube_014.fbx
  artifactKey: Guid(ce142f227d26bbc488147b1cb155517d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_014.fbx using Guid(ce142f227d26bbc488147b1cb155517d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4f9109a70600b3ae4b9629f1d762d220') in 0.0384616 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000047 seconds.
  path: Assets/_Game/Models/Area-1/F1.fbx
  artifactKey: Guid(6fb1a43daac9fc44faecefc8d60a4af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/F1.fbx using Guid(6fb1a43daac9fc44faecefc8d60a4af1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a12cc9da950488a948e10fff7bb931dc') in 0.0522387 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_Game/Models/Area-1/Cube_012.fbx
  artifactKey: Guid(0a3f69fa6bf1f9b4b94bea8c2ef1c9c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_012.fbx using Guid(0a3f69fa6bf1f9b4b94bea8c2ef1c9c6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6e22d63d109c483988f5b22bac06707c') in 0.030304 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000095 seconds.
  path: Assets/_Game/Models/Area-1/Debrey.fbx
  artifactKey: Guid(6a3599c84381b8340943ff1fffa6d661) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Debrey.fbx using Guid(6a3599c84381b8340943ff1fffa6d661) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f734db324750d30c017bbe4bdddef84') in 0.0302826 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Models/Area-1/Cylinder.fbx
  artifactKey: Guid(dcd41fe5ce99dfd47b37bc2df75c5388) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cylinder.fbx using Guid(dcd41fe5ce99dfd47b37bc2df75c5388) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1dc16e3cc0a872d84554776c4fade33a') in 0.0338413 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Cube_017.fbx
  artifactKey: Guid(d05bae6ffab699e448101f163b4eb4eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_017.fbx using Guid(d05bae6ffab699e448101f163b4eb4eb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '25004d1079d2d46db7cca7da442cc433') in 0.043164 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Cube_001.fbx
  artifactKey: Guid(64ef877d8cd62b44b92b5ee2b02cc197) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_001.fbx using Guid(64ef877d8cd62b44b92b5ee2b02cc197) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'acd82bf8bb166dba8ad842eb7d687b8d') in 0.0368386 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_Game/Models/Area-1/Cube_004.fbx
  artifactKey: Guid(f1a3c542d8cb8ea46a1bf6d929b49e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_004.fbx using Guid(f1a3c542d8cb8ea46a1bf6d929b49e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2f03a7129100590d29f33d87a94091ef') in 0.0276579 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_Game/Models/Area-1/Cube_008.fbx
  artifactKey: Guid(0fd8a9e74b05b0e49a0e9e977acbc033) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_008.fbx using Guid(0fd8a9e74b05b0e49a0e9e977acbc033) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af264e24fb5dbd256b08de56b5ec2a77') in 0.0310716 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/_Game/Models/Area-1/Cube_013.fbx
  artifactKey: Guid(22673c4a03585f44980bb59588e4154d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_013.fbx using Guid(22673c4a03585f44980bb59588e4154d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cda201c5befcc901f55cbd66b6eed80b') in 0.0289492 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/_Game/Models/Area-1/Cube_016.fbx
  artifactKey: Guid(8a4f6b05db1d05a46b279ccfa95c67ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cube_016.fbx using Guid(8a4f6b05db1d05a46b279ccfa95c67ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a17b9edb324e9208b0ee1b4a52fb708d') in 0.0417483 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.009214 seconds.
  path: Assets/_Game/Models/Area-1/FirstGround.fbx
  artifactKey: Guid(4dad607ce984cbe42bfc390ae86afa2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/FirstGround.fbx using Guid(4dad607ce984cbe42bfc390ae86afa2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f6b683b7f4561eedb7d5e4aecf82732a') in 0.0273602 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Morror.fbx
  artifactKey: Guid(926cacaca291bb241b3f3564380e1a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Morror.fbx using Guid(926cacaca291bb241b3f3564380e1a53) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'caf6caf25f816a1728a968876b1c2bd7') in 0.0673005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/cable.fbx
  artifactKey: Guid(90541e5e119c8ad4a95deff4d22c21f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/cable.fbx using Guid(90541e5e119c8ad4a95deff4d22c21f5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd893c46eb3e379fa9863eb0ac53f1938') in 0.0389005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Models/Area-1/Tube_001.fbx
  artifactKey: Guid(c9662ff4c0b8d5842b2452c3aa11f576) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Tube_001.fbx using Guid(c9662ff4c0b8d5842b2452c3aa11f576) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c14ce7ce6c49bb8469a70dc77d9218f2') in 0.0334428 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Blockout_Stairs002.fbx
  artifactKey: Guid(b98dff1e2a644974cb037ada140f913d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Blockout_Stairs002.fbx using Guid(b98dff1e2a644974cb037ada140f913d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f190ed6f33542d82149a99035bb0d8aa') in 0.027225 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Mega.fbx
  artifactKey: Guid(999922037ce911f4f8f73bb8def097ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Mega.fbx using Guid(999922037ce911f4f8f73bb8def097ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '43d8ecff6aba038be2d4596f768965a7') in 0.0291931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/_Game/Models/Area-1/Cable_Mass.fbx
  artifactKey: Guid(abcf7677e55d2f646b8a38c519fed07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Cable_Mass.fbx using Guid(abcf7677e55d2f646b8a38c519fed07b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5c561924e55c9e381b724ac9c0abfd5d') in 0.0390345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/AElevatorBase.fbx
  artifactKey: Guid(a25978aa579745d44acd5f0be40c50c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/AElevatorBase.fbx using Guid(a25978aa579745d44acd5f0be40c50c5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e7438676217f0bb0cfd0a23fd2dd08fe') in 0.0384132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000051 seconds.
  path: Assets/_Game/Models/Area-1/test22.fbx
  artifactKey: Guid(85516922031f3fc48baa0339d79658f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/test22.fbx using Guid(85516922031f3fc48baa0339d79658f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f4d7c53c3d5aaf78abc9cc98640ccbe') in 0.5977397 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 150

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_Game/Models/Area-1/ACube_001.fbx
  artifactKey: Guid(f709a0b29578aa84895199dbaa4b1a1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/ACube_001.fbx using Guid(f709a0b29578aa84895199dbaa4b1a1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '476891c9bc625c29847e83bd58a39a63') in 0.0325719 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000039 seconds.
  path: Assets/_Game/Models/Area-1/Rocket.fbx
  artifactKey: Guid(61bee926499a7f548a23a040c5ee90f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Rocket.fbx using Guid(61bee926499a7f548a23a040c5ee90f4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d81a98000da8994b64c3bb78bf77600') in 0.0423561 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_Game/Models/Area-1/Blockout.fbx
  artifactKey: Guid(703287888e46a9e40a5df79ed40eec4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Blockout.fbx using Guid(703287888e46a9e40a5df79ed40eec4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2e84f455b90ddca26e5e17045642e46c') in 0.3947364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000085 seconds.
  path: Assets/_Game/Models/Area-1/Bestagon.fbx
  artifactKey: Guid(28fda9469ec335d4080e82ce3431740b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/Bestagon.fbx using Guid(28fda9469ec335d4080e82ce3431740b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf3724c0fa34eab2028e9086bc2902ee') in 0.031208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_Game/Models/Area-1/cable_002.fbx
  artifactKey: Guid(48fec707ddbb29d49bac044cb602e54e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Models/Area-1/cable_002.fbx using Guid(48fec707ddbb29d49bac044cb602e54e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8fa7084ede92d8783576147fb75da7cf') in 0.0387595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.28 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10511 unused Assets / (18.1 MB). Loaded Objects now: 14764.
Memory consumption went from 433.2 MB to 415.0 MB.
Total: 33.052800 ms (FindLiveObjects: 2.270500 ms CreateObjectMapping: 1.788500 ms MarkObjects: 22.065600 ms  DeleteObjects: 6.925700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.26 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.21 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (21.3 MB). Loaded Objects now: 14763.
Memory consumption went from 433.2 MB to 411.8 MB.
Total: 31.987900 ms (FindLiveObjects: 2.343600 ms CreateObjectMapping: 1.858500 ms MarkObjects: 20.424100 ms  DeleteObjects: 7.360800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Leak Detected : Persistent allocates 8 individual allocations. To find out more please enable 'Preferences > Jobs > Leak Detection Level > Enabled With Stack Trace' and reproduce the leak again.
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.303 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 41.79 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.236 seconds
Domain Reload Profiling: 6301ms
	BeginReloadAssembly (1302ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (151ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (169ms)
	RebuildCommonClasses (301ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (325ms)
	LoadAllAssembliesAndSetupDomain (2109ms)
		LoadAssemblies (1942ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (684ms)
			TypeCache.Refresh (24ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (635ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (2237ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1892ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (848ms)
			ProcessInitializeOnLoadAttributes (609ms)
			ProcessInitializeOnLoadMethodAttributes (390ms)
			AfterProcessingInitializeOnLoad (33ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (50ms)
Refreshing native plugins compatible for Editor in 31.30 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (20.7 MB). Loaded Objects now: 11803.
Memory consumption went from 366.0 MB to 345.3 MB.
Total: 21.062700 ms (FindLiveObjects: 1.115400 ms CreateObjectMapping: 0.710400 ms MarkObjects: 8.780400 ms  DeleteObjects: 10.455200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.08 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.22 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (19.7 MB). Loaded Objects now: 11803.
Memory consumption went from 365.5 MB to 345.8 MB.
Total: 17.521000 ms (FindLiveObjects: 1.064100 ms CreateObjectMapping: 1.165700 ms MarkObjects: 8.712200 ms  DeleteObjects: 6.577800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.236 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.73 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.666 seconds
Domain Reload Profiling: 4871ms
	BeginReloadAssembly (1169ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (152ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (151ms)
	RebuildCommonClasses (61ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (1888ms)
		LoadAssemblies (1869ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (641ms)
			TypeCache.Refresh (35ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (576ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1667ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1279ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (15ms)
			BeforeProcessingInitializeOnLoad (413ms)
			ProcessInitializeOnLoadAttributes (561ms)
			ProcessInitializeOnLoadMethodAttributes (261ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 23.71 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (20.6 MB). Loaded Objects now: 11806.
Memory consumption went from 365.9 MB to 345.2 MB.
Total: 22.328400 ms (FindLiveObjects: 1.618300 ms CreateObjectMapping: 2.276200 ms MarkObjects: 8.286900 ms  DeleteObjects: 10.146000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.58 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.17 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (19.4 MB). Loaded Objects now: 11806.
Memory consumption went from 365.6 MB to 346.2 MB.
Total: 21.751600 ms (FindLiveObjects: 1.590800 ms CreateObjectMapping: 1.869100 ms MarkObjects: 9.812800 ms  DeleteObjects: 8.477700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.60 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.15 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (16.8 MB). Loaded Objects now: 11806.
Memory consumption went from 365.6 MB to 348.8 MB.
Total: 52.234900 ms (FindLiveObjects: 1.922800 ms CreateObjectMapping: 1.578100 ms MarkObjects: 39.239500 ms  DeleteObjects: 9.492900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.614 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.95 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.609 seconds
Domain Reload Profiling: 4184ms
	BeginReloadAssembly (618ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (56ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (123ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (1784ms)
		LoadAssemblies (1287ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (719ms)
			TypeCache.Refresh (26ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (665ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1609ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1272ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (441ms)
			ProcessInitializeOnLoadAttributes (539ms)
			ProcessInitializeOnLoadMethodAttributes (251ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 22.58 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.21 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (20.6 MB). Loaded Objects now: 11809.
Memory consumption went from 366.0 MB to 345.4 MB.
Total: 19.275300 ms (FindLiveObjects: 1.601600 ms CreateObjectMapping: 1.286900 ms MarkObjects: 9.479800 ms  DeleteObjects: 6.906100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.376 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.17 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.594 seconds
Domain Reload Profiling: 3945ms
	BeginReloadAssembly (704ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (88ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (236ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (1493ms)
		LoadAssemblies (1157ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (555ms)
			TypeCache.Refresh (38ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (498ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (420ms)
			ProcessInitializeOnLoadAttributes (594ms)
			ProcessInitializeOnLoadMethodAttributes (244ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 34.17 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.20 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (18.6 MB). Loaded Objects now: 11812.
Memory consumption went from 366.1 MB to 347.4 MB.
Total: 25.681100 ms (FindLiveObjects: 1.840700 ms CreateObjectMapping: 2.238500 ms MarkObjects: 12.362900 ms  DeleteObjects: 9.237500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Error loading the file 'Library/BuildProfiles/SharedProfile.asset'. File is either empty or corrupted, please verify the file contents.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForgetInternal (string,UnityEngine.Object[],bool)
UnityEditorInternal.InternalEditorUtility:SaveToSerializedFileAndForget (UnityEngine.Object[],string,bool)
UnityEditor.Build.Profile.BuildProfileContext:SaveBuildProfileInProject (UnityEditor.Build.Profile.BuildProfile)
UnityEditor.Build.Profile.BuildProfileContext:OnDisable ()

[ line 0]

Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.089 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 20.17 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.340 seconds
Domain Reload Profiling: 3391ms
	BeginReloadAssembly (521ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (54ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1354ms)
		LoadAssemblies (1137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (427ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (384ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1341ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1096ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (396ms)
			ProcessInitializeOnLoadAttributes (453ms)
			ProcessInitializeOnLoadMethodAttributes (219ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 20.60 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.12 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (19.9 MB). Loaded Objects now: 11816.
Memory consumption went from 366.2 MB to 346.3 MB.
Total: 14.620100 ms (FindLiveObjects: 1.040100 ms CreateObjectMapping: 0.791700 ms MarkObjects: 6.223500 ms  DeleteObjects: 6.563500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.83 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.23 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (18.6 MB). Loaded Objects now: 11816.
Memory consumption went from 365.9 MB to 347.3 MB.
Total: 23.085800 ms (FindLiveObjects: 2.790400 ms CreateObjectMapping: 2.020000 ms MarkObjects: 9.007700 ms  DeleteObjects: 9.266200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.67 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.16 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (19.8 MB). Loaded Objects now: 11816.
Memory consumption went from 365.9 MB to 346.2 MB.
Total: 18.989100 ms (FindLiveObjects: 1.614500 ms CreateObjectMapping: 0.963800 ms MarkObjects: 8.783200 ms  DeleteObjects: 7.626100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 79.71 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.23 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10509 unused Assets / (19.6 MB). Loaded Objects now: 11817.
Memory consumption went from 365.9 MB to 346.3 MB.
Total: 31.587100 ms (FindLiveObjects: 2.233400 ms CreateObjectMapping: 2.007300 ms MarkObjects: 14.121800 ms  DeleteObjects: 13.222800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.872 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.79 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.934 seconds
Domain Reload Profiling: 4772ms
	BeginReloadAssembly (803ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (102ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (114ms)
	RebuildCommonClasses (101ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (1840ms)
		LoadAssemblies (1409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (715ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (672ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1935ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1652ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (502ms)
			ProcessInitializeOnLoadAttributes (764ms)
			ProcessInitializeOnLoadMethodAttributes (353ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (48ms)
Refreshing native plugins compatible for Editor in 25.55 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.20 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10567 unused Assets / (19.2 MB). Loaded Objects now: 11820.
Memory consumption went from 366.3 MB to 347.2 MB.
Total: 17.108300 ms (FindLiveObjects: 1.657500 ms CreateObjectMapping: 1.451900 ms MarkObjects: 7.083500 ms  DeleteObjects: 6.914400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2091.388220 seconds.
  path: Assets/_Game/Scripts/unity-fog-fixer.cs
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-fog-fixer.cs using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd6f331aeb54a4ecb53a0d877b970d2d0') in 0.2233847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.317 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 24.20 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.881 seconds
Domain Reload Profiling: 4172ms
	BeginReloadAssembly (602ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (62ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (1561ms)
		LoadAssemblies (1144ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (582ms)
			TypeCache.Refresh (29ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (519ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1882ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1597ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (455ms)
			ProcessInitializeOnLoadAttributes (651ms)
			ProcessInitializeOnLoadMethodAttributes (455ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 26.95 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.17 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10567 unused Assets / (20.0 MB). Loaded Objects now: 11823.
Memory consumption went from 366.5 MB to 346.5 MB.
Total: 22.265400 ms (FindLiveObjects: 1.724800 ms CreateObjectMapping: 2.002500 ms MarkObjects: 9.741200 ms  DeleteObjects: 8.795100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.957 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.97 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.715 seconds
Domain Reload Profiling: 5610ms
	BeginReloadAssembly (680ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (76ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (119ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (110ms)
	LoadAllAssembliesAndSetupDomain (2962ms)
		LoadAssemblies (2796ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (543ms)
			TypeCache.Refresh (22ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (496ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1715ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1430ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (375ms)
			ProcessInitializeOnLoadAttributes (756ms)
			ProcessInitializeOnLoadMethodAttributes (263ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 38.11 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.17 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10568 unused Assets / (19.1 MB). Loaded Objects now: 11827.
Memory consumption went from 366.6 MB to 347.5 MB.
Total: 21.978300 ms (FindLiveObjects: 1.581100 ms CreateObjectMapping: 1.795800 ms MarkObjects: 10.208400 ms  DeleteObjects: 8.391500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 462.572067 seconds.
  path: Assets/_Game/Scripts/unity-fog-build-fix.cs
  artifactKey: Guid(8735604e7b39d4e418b5636c6228795b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-fog-build-fix.cs using Guid(8735604e7b39d4e418b5636c6228795b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '347d546c05b87462b2c69705c957e8ed') in 0.013449 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.749312 seconds.
  path: Assets/_Game/Scripts/unity-performance-diagnostic.cs
  artifactKey: Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/unity-performance-diagnostic.cs using Guid(2202a040ca663de4aab5349a46ca9993) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4de96c6e5ef05c40d8e45078db308e20') in 0.0010742 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.75 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10507 unused Assets / (17.1 MB). Loaded Objects now: 11824.
Memory consumption went from 366.3 MB to 349.2 MB.
Total: 26.769400 ms (FindLiveObjects: 1.504600 ms CreateObjectMapping: 1.925300 ms MarkObjects: 13.066000 ms  DeleteObjects: 10.272000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.967 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 21.28 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.483 seconds
Domain Reload Profiling: 3416ms
	BeginReloadAssembly (397ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (1398ms)
		LoadAssemblies (1104ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (462ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (424ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1484ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1170ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (364ms)
			ProcessInitializeOnLoadAttributes (542ms)
			ProcessInitializeOnLoadMethodAttributes (225ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 24.98 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10565 unused Assets / (21.0 MB). Loaded Objects now: 11827.
Memory consumption went from 366.7 MB to 345.6 MB.
Total: 19.436100 ms (FindLiveObjects: 1.391800 ms CreateObjectMapping: 1.026000 ms MarkObjects: 9.310700 ms  DeleteObjects: 7.706600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.090 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 23.96 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.669 seconds
Domain Reload Profiling: 3729ms
	BeginReloadAssembly (406ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (119ms)
	LoadAllAssembliesAndSetupDomain (1429ms)
		LoadAssemblies (1085ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (519ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (468ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1670ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1403ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (414ms)
			ProcessInitializeOnLoadAttributes (652ms)
			ProcessInitializeOnLoadMethodAttributes (296ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 23.26 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.17 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10565 unused Assets / (21.5 MB). Loaded Objects now: 11830.
Memory consumption went from 366.8 MB to 345.3 MB.
Total: 20.444300 ms (FindLiveObjects: 1.720600 ms CreateObjectMapping: 1.914400 ms MarkObjects: 9.322900 ms  DeleteObjects: 7.484600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 33.43 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10507 unused Assets / (20.5 MB). Loaded Objects now: 11830.
Memory consumption went from 366.5 MB to 346.0 MB.
Total: 19.969400 ms (FindLiveObjects: 1.633500 ms CreateObjectMapping: 1.430600 ms MarkObjects: 9.291000 ms  DeleteObjects: 7.613000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.799 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.71 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.514 seconds
Domain Reload Profiling: 4287ms
	BeginReloadAssembly (559ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (73ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (91ms)
	LoadAllAssembliesAndSetupDomain (2037ms)
		LoadAssemblies (1635ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (702ms)
			TypeCache.Refresh (228ms)
				TypeCache.ScanAssembly (43ms)
			BuildScriptInfoCaches (451ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1514ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1190ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (393ms)
			ProcessInitializeOnLoadAttributes (512ms)
			ProcessInitializeOnLoadMethodAttributes (247ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 25.87 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.29 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (19.3 MB). Loaded Objects now: 11834.
Memory consumption went from 366.9 MB to 347.6 MB.
Total: 17.407700 ms (FindLiveObjects: 0.999300 ms CreateObjectMapping: 0.629300 ms MarkObjects: 9.062700 ms  DeleteObjects: 6.715100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.608 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 28.46 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.463 seconds
Domain Reload Profiling: 3046ms
	BeginReloadAssembly (338ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (36ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (39ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (50ms)
	LoadAllAssembliesAndSetupDomain (1144ms)
		LoadAssemblies (894ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (383ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (343ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1464ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1150ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (373ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (222ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 29.18 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.13 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (19.3 MB). Loaded Objects now: 11837.
Memory consumption went from 367.0 MB to 347.7 MB.
Total: 15.215500 ms (FindLiveObjects: 1.225300 ms CreateObjectMapping: 1.266000 ms MarkObjects: 7.093300 ms  DeleteObjects: 5.630000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.023 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.49 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.925 seconds
Domain Reload Profiling: 3913ms
	BeginReloadAssembly (417ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (85ms)
	LoadAllAssembliesAndSetupDomain (1431ms)
		LoadAssemblies (1090ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (507ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (461ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1925ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1646ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (582ms)
			ProcessInitializeOnLoadAttributes (714ms)
			ProcessInitializeOnLoadMethodAttributes (310ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 30.56 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.18 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (20.2 MB). Loaded Objects now: 11840.
Memory consumption went from 367.1 MB to 346.9 MB.
Total: 23.565300 ms (FindLiveObjects: 1.513500 ms CreateObjectMapping: 5.167300 ms MarkObjects: 9.819800 ms  DeleteObjects: 7.062800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.15 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.17 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (20.1 MB). Loaded Objects now: 11840.
Memory consumption went from 366.8 MB to 346.7 MB.
Total: 18.229500 ms (FindLiveObjects: 1.500300 ms CreateObjectMapping: 1.017700 ms MarkObjects: 8.342300 ms  DeleteObjects: 7.367900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 46.11 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10508 unused Assets / (18.8 MB). Loaded Objects now: 11840.
Memory consumption went from 366.8 MB to 348.0 MB.
Total: 37.023300 ms (FindLiveObjects: 1.627600 ms CreateObjectMapping: 3.671000 ms MarkObjects: 18.871800 ms  DeleteObjects: 12.851500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.030 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 25.27 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.479 seconds
Domain Reload Profiling: 3474ms
	BeginReloadAssembly (486ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (101ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (70ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1386ms)
		LoadAssemblies (1093ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (459ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (427ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1479ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1211ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (464ms)
			ProcessInitializeOnLoadAttributes (492ms)
			ProcessInitializeOnLoadMethodAttributes (221ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 21.71 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.16 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (19.4 MB). Loaded Objects now: 11843.
Memory consumption went from 367.2 MB to 347.8 MB.
Total: 15.046400 ms (FindLiveObjects: 0.983300 ms CreateObjectMapping: 0.629600 ms MarkObjects: 7.389000 ms  DeleteObjects: 6.043500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: F4-HMNT-RG52-B4U2-GDQY-XXXX
  Product: Unity Personal
  Type: ULF
  Expiration: Unlimited
[Licensing::Module] License group:
  Id: 9070987079441-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.767 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.24 ms, found 35 plugins.
Native extension for WindowsStandalone target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Failed to list local packages
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:Install2DEnhancerPackage () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:29)
UnityEditor.U2D.Sprites.AIIntegration.AIIntegration:.cctor () (at ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs:20)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.unity.2d.sprite@28296e5d02fb/Editor/AIIntegration/AIIntegration.cs Line: 29)

System.ArgumentNullException: Value cannot be null.
Parameter name: collection
  at System.Collections.Generic.List`1[T]..ctor (System.Collections.Generic.IEnumerable`1[T] collection) [0x00009] in <6460d52f11fa4eaeabfca1cf2e9eca8f>:0 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration.Install2DEnhancerPackage () [0x0002b] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:31 
  at UnityEditor.U2D.Sprites.AIIntegration.AIIntegration..cctor () [0x00015] in .\Library\PackageCache\com.unity.2d.sprite@28296e5d02fb\Editor\AIIntegration\AIIntegration.cs:20 
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.095 seconds
Domain Reload Profiling: 4822ms
	BeginReloadAssembly (865ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (119ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (68ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (80ms)
	LoadAllAssembliesAndSetupDomain (1692ms)
		LoadAssemblies (1486ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (637ms)
			TypeCache.Refresh (76ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (521ms)
			ResolveRequiredComponents (30ms)
	FinalizeReload (2096ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1771ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (649ms)
			ProcessInitializeOnLoadAttributes (721ms)
			ProcessInitializeOnLoadMethodAttributes (355ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 47.63 ms, found 35 plugins.
Preloading 1 native plugins for Editor in 0.19 ms.
Unloading 59 Unused Serialized files (Serialized files now loaded: 0)
Unloading 10566 unused Assets / (17.6 MB). Loaded Objects now: 11846.
Memory consumption went from 367.3 MB to 349.7 MB.
Total: 27.324100 ms (FindLiveObjects: 1.446900 ms CreateObjectMapping: 1.485300 ms MarkObjects: 11.108400 ms  DeleteObjects: 13.281900 ms)

Prepare: number of updated asset objects reloaded= 0
