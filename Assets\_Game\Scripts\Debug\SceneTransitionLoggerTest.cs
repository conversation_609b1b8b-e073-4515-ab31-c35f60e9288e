using UnityEngine;
using System.IO;

/// <summary>
/// Test component to verify scene transition logging is working
/// Add this to any GameObject in the scene to test the scene transition logging system
/// </summary>
public class SceneTransitionLoggerTest : MonoBehaviour
{
    [Header("Scene Transition Logger Test")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private KeyCode testKey = KeyCode.F10;
    [SerializeField] private KeyCode clearLogKey = KeyCode.F11;
    
    private void Start()
    {
        if (runTestOnStart)
        {
            TestSceneTransitionLogging();
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            TestSceneTransitionLogging();
        }
        
        if (Input.GetKeyDown(clearLogKey))
        {
            ClearLogFile();
        }
    }
    
    [ContextMenu("Test Scene Transition Logging")]
    public void TestSceneTransitionLogging()
    {
        Debug.Log("[SceneTransitionLoggerTest] Testing scene transition logging...");
        
        // Test basic logging
        SceneTransitionLogger.LogTransitionStep("TEST_BASIC", "Basic logging test");
        SceneTransitionLogger.LogPotentialFreeze("Test freeze detection");
        
        // Check if log file exists and show its location
        string logPath = Path.Combine(Application.persistentDataPath, "scene_transition.log");
        if (File.Exists(logPath))
        {
            Debug.Log($"[SceneTransitionLoggerTest] Log file exists at: {logPath}");
            
            // Show last few lines of the log
            try
            {
                string[] lines = File.ReadAllLines(logPath);
                int startIndex = Mathf.Max(0, lines.Length - 10);
                Debug.Log("[SceneTransitionLoggerTest] Last 10 log entries:");
                for (int i = startIndex; i < lines.Length; i++)
                {
                    Debug.Log($"  {lines[i]}");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[SceneTransitionLoggerTest] Error reading log file: {e.Message}");
            }
        }
        else
        {
            Debug.LogWarning($"[SceneTransitionLoggerTest] Log file not found at: {logPath}");
        }
    }
    
    [ContextMenu("Simulate Scene Transition")]
    public void SimulateSceneTransition()
    {
        Debug.Log("[SceneTransitionLoggerTest] Simulating scene transition...");
        
        SceneTransitionLogger.LogSceneTransitionStart("TestScene", "TargetScene");
        SceneTransitionLogger.LogTransitionStep("SIMULATE_STEP_1", "Simulated step 1");
        SceneTransitionLogger.LogTransitionStep("SIMULATE_STEP_2", "Simulated step 2");
        SceneTransitionLogger.LogPotentialFreeze("Simulated potential freeze point");
        SceneTransitionLogger.LogTransitionStep("SIMULATE_STEP_3", "Simulated step 3");
        SceneTransitionLogger.LogSceneTransitionEnd("TargetScene");
        
        Debug.Log("[SceneTransitionLoggerTest] Scene transition simulation complete");
    }
    
    [ContextMenu("Open Log File Location")]
    public void OpenLogFileLocation()
    {
        string logPath = Path.Combine(Application.persistentDataPath, "scene_transition.log");
        string directory = Path.GetDirectoryName(logPath);
        
        Debug.Log($"[SceneTransitionLoggerTest] Log directory: {directory}");
        
        // Try to open the directory in file explorer
        try
        {
            System.Diagnostics.Process.Start(directory);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[SceneTransitionLoggerTest] Could not open directory: {e.Message}");
        }
    }
    
    [ContextMenu("Clear Log File")]
    public void ClearLogFile()
    {
        SceneTransitionLogger.ClearLogFile();
        Debug.Log("[SceneTransitionLoggerTest] Log file cleared.");
    }
    
    [ContextMenu("Enable/Disable Logging")]
    public void ToggleLogging()
    {
        // This would need to be implemented in SceneTransitionLogger
        Debug.Log("[SceneTransitionLoggerTest] Logging toggle requested (check SceneTransitionLogger for implementation)");
    }
    
    [ContextMenu("Show System Info")]
    public void ShowSystemInfo()
    {
        Debug.Log($"[SceneTransitionLoggerTest] System Info:");
        Debug.Log($"  Platform: {Application.platform}");
        Debug.Log($"  Unity Version: {Application.unityVersion}");
        Debug.Log($"  System Memory: {SystemInfo.systemMemorySize}MB");
        Debug.Log($"  Graphics Memory: {SystemInfo.graphicsMemorySize}MB");
        Debug.Log($"  Processor: {SystemInfo.processorType}");
        Debug.Log($"  Processor Count: {SystemInfo.processorCount}");
        Debug.Log($"  Graphics Device: {SystemInfo.graphicsDeviceName}");
        Debug.Log($"  Target Frame Rate: {Application.targetFrameRate}");
        Debug.Log($"  VSync Count: {QualitySettings.vSyncCount}");
        
        // Log this info to the transition log as well
        SceneTransitionLogger.LogTransitionStep("SYSTEM_INFO", 
            $"Platform: {Application.platform}, Memory: {SystemInfo.systemMemorySize}MB, " +
            $"CPU: {SystemInfo.processorType} ({SystemInfo.processorCount} cores), " +
            $"GPU: {SystemInfo.graphicsDeviceName}");
    }
    
    private void OnGUI()
    {
        // Show helpful info on screen
        GUILayout.BeginArea(new Rect(10, 10, 400, 200));
        GUILayout.Label("Scene Transition Logger Test", new GUIStyle(GUI.skin.label) { fontSize = 16, fontStyle = FontStyle.Bold });
        GUILayout.Label($"Press {testKey} to test logging");
        GUILayout.Label($"Press {clearLogKey} to clear log file");
        GUILayout.Label("Check console for log file location");
        
        if (GUILayout.Button("Test Logging"))
        {
            TestSceneTransitionLogging();
        }
        
        if (GUILayout.Button("Simulate Transition"))
        {
            SimulateSceneTransition();
        }
        
        if (GUILayout.Button("Open Log Folder"))
        {
            OpenLogFileLocation();
        }
        
        if (GUILayout.Button("Clear Log"))
        {
            ClearLogFile();
        }
        
        GUILayout.EndArea();
    }
}
