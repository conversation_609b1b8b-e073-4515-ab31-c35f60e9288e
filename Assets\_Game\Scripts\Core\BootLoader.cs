using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
using System.Collections;
#if UNITY_EDITOR
using UnityEditor;
#endif

/// <summary>
/// Minimal boot loader that shows a lightweight loading UI and loads the main scene asynchronously.
/// Usage:
/// - Create an empty scene named "<PERSON>ot" (or any name) with a GameObject that has this component
/// - Put that scene at index 0 in Build Settings
/// - Set the sceneToLoad to your main scene name (e.g., "Main")
/// </summary>
public class BootLoader : MonoBehaviour
{
	[Header("Scene")]
	[SerializeField] private string sceneToLoad = "";

#if UNITY_EDITOR
	// Assign a SceneAsset in editor; we copy its name into sceneToLoad via OnValidate
	[SerializeField] private SceneAsset sceneAsset;
#endif

	[SerializeField] private bool autoActivateOnReady = true;

	[Header("UI Text")] 
	[SerializeField] private string loadingText = "Initializing Persistence Engine";

	[Header("Animation")]
	[SerializeField] private float dotsInterval = 0.35f;
	[SerializeField] private float pulseSpeed = 1.5f;
	[SerializeField] private float pulseIntensity = 0.2f;


	[Header("UX")]
	[SerializeField] private float fadeOutDuration = 1.0f;
	[SerializeField] private float fadeInDuration = 1.0f;
	[SerializeField] private float minDisplayTime = 2.0f;

	[Header("Performance")]
	[SerializeField] private bool capFpsDuringBoot = true;
	[SerializeField] private int bootTargetFps = 60;

	// Runtime UI
	private Canvas _canvas;
	private CanvasScaler _scaler;
	private GraphicRaycaster _raycaster;
	private CanvasGroup _group;
	private Image _backdrop;
	private Text _loadingText;
	private int _prevVSync;
	private int _prevTargetFps;

	private void Awake()
	{
		// Log boot start
		SceneTransitionLogger.LogTransitionStep("BOOTLOADER_AWAKE", "BootLoader Awake started");

		// Temporarily cap FPS to avoid excessive GPU/CPU load during boot
		_prevVSync = QualitySettings.vSyncCount;
		_prevTargetFps = Application.targetFrameRate;
		if (capFpsDuringBoot)
		{
			QualitySettings.vSyncCount = 0; // ensure targetFrameRate takes effect
			Application.targetFrameRate = Mathf.Max(30, bootTargetFps);
			SceneTransitionLogger.LogTransitionStep("FPS_CAPPED", $"FPS capped to {bootTargetFps} during boot");
		}

		DontDestroyOnLoad(gameObject);
		SceneTransitionLogger.LogTransitionStep("DONT_DESTROY_SET", "BootLoader set to DontDestroyOnLoad");

		CreateOverlayUI();
		SceneTransitionLogger.LogTransitionStep("OVERLAY_UI_CREATED", "Boot overlay UI created");
	}

	private void Start()
	{
		SceneTransitionLogger.LogTransitionStep("BOOTLOADER_START", "BootLoader Start method called");
		StartCoroutine(LoadSceneAsyncWithOverlay());
	}

#if UNITY_EDITOR
	private void OnValidate()
	{
		if (sceneAsset != null)
		{
			string path = AssetDatabase.GetAssetPath(sceneAsset);
			if (!string.IsNullOrEmpty(path))
			{
				string name = System.IO.Path.GetFileNameWithoutExtension(path);
				if (!string.IsNullOrEmpty(name))
				{
					sceneToLoad = name;
				}
			}
		}
	}
#endif

	private void CreateOverlayUI()
	{
		// Canvas
		var canvasGO = new GameObject("LoadingCanvas");
		canvasGO.transform.SetParent(transform);
		_canvas = canvasGO.AddComponent<Canvas>();
		_canvas.renderMode = RenderMode.ScreenSpaceOverlay;
		_canvas.sortingOrder = 10000; // ensure always on top
		_scaler = canvasGO.AddComponent<CanvasScaler>();
		_scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
		_scaler.referenceResolution = new Vector2(1920, 1080);
		_raycaster = canvasGO.AddComponent<GraphicRaycaster>();
		_group = canvasGO.AddComponent<CanvasGroup>();
		_group.alpha = 1f;

		// Backdrop
		var bgGO = new GameObject("Backdrop", typeof(RectTransform));
		bgGO.transform.SetParent(canvasGO.transform, false);
		_backdrop = bgGO.AddComponent<Image>();
		_backdrop.color = new Color(0f, 0f, 0f, 1f);
		var bgRt = bgGO.GetComponent<RectTransform>();
		bgRt.anchorMin = Vector2.zero;
		bgRt.anchorMax = Vector2.one;
		bgRt.offsetMin = Vector2.zero;
		bgRt.offsetMax = Vector2.zero;

		// Loading text
		_loadingText = CreateText(canvasGO.transform, loadingText, 24, new Vector2(0.5f, 0.5f));

		// Style to match DeathManager look (font + subtle outline)
		Font serif = Resources.Load<Font>("InstrumentSerif-Regular");
		if (serif != null)
		{
			_loadingText.font = serif;
		}
		var textOutline = _loadingText.gameObject.AddComponent<UnityEngine.UI.Outline>();
		textOutline.effectColor = new Color(0.1f, 0.1f, 0.1f, 0.8f);
		textOutline.effectDistance = new Vector2(2, -2);
	}

	private Text CreateText(Transform parent, string text, int size, Vector2 anchor)
	{
		// Ensure RectTransform exists to avoid invalid casts on some Unity versions
		var go = new GameObject("Text", typeof(RectTransform));
		go.transform.SetParent(parent, false);
		var t = go.AddComponent<Text>();
		t.text = text;
		t.fontSize = size;
		t.color = Color.white;
		t.alignment = TextAnchor.MiddleCenter;
		t.horizontalOverflow = HorizontalWrapMode.Overflow;
		t.verticalOverflow = VerticalWrapMode.Overflow;
		// Use new built-in runtime font; Arial.ttf is deprecated in newer Unity versions
		t.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
		var rt = go.GetComponent<RectTransform>();
		rt.anchorMin = anchor;
		rt.anchorMax = anchor;
		rt.pivot = new Vector2(0.5f, 0.5f);
		rt.sizeDelta = new Vector2(600, 60);
		return t;
	}



	private IEnumerator LoadSceneAsyncWithOverlay()
	{
		SceneTransitionLogger.LogSceneTransitionStart("Boot", sceneToLoad);
		SceneTransitionLogger.LogTransitionStep("LOAD_COROUTINE_START", "LoadSceneAsyncWithOverlay coroutine started");

		// Start with fade-in
		_group.alpha = 0f;
		SceneTransitionLogger.LogTransitionStep("FADE_IN_START", "Starting fade-in animation");
		yield return StartCoroutine(FadeIn());
		SceneTransitionLogger.LogTransitionStep("FADE_IN_COMPLETE", "Fade-in animation completed");

		// Validate target scene
		string target = sceneToLoad;
		SceneTransitionLogger.LogTransitionStep("SCENE_VALIDATION", $"Validating target scene: '{target}'");

		if (string.IsNullOrEmpty(target) || !Application.CanStreamedLevelBeLoaded(target))
		{
			SceneTransitionLogger.LogTransitionStep("SCENE_VALIDATION_FAILED", $"Scene validation failed for '{target}'");
			_loadingText.text = "Error";
			yield return new WaitForSeconds(0.5f);
			_loadingText.text = string.IsNullOrEmpty(target)
				? "No scene specified. Assign a scene in BootLoader."
				: $"Scene not found/in Build Settings: '{target}'";
			yield return StartCoroutine(WaitForAnyKeyThenQuit());
			yield break;
		}

		SceneTransitionLogger.LogTransitionStep("SCENE_VALIDATION_SUCCESS", $"Scene '{target}' validated successfully");

		float shownStart = Time.unscaledTime;
		SceneTransitionLogger.LogTransitionStep("ASYNC_LOAD_START", $"Starting async load of '{target}'");

		// This is the critical point where freezes often occur
		SceneTransitionLogger.LogPotentialFreeze("About to call SceneManager.LoadSceneAsync");
		AsyncOperation op = SceneManager.LoadSceneAsync(target, LoadSceneMode.Single);
		SceneTransitionLogger.LogTransitionStep("ASYNC_LOAD_INITIATED", "SceneManager.LoadSceneAsync called successfully");

		op.allowSceneActivation = false;
		SceneTransitionLogger.LogTransitionStep("SCENE_ACTIVATION_DISABLED", "Scene activation disabled, waiting for load");

		// Wait for scene to load (no progress bar needed)
		int progressCheckCount = 0;
		while (op != null && op.progress < 0.9f)
		{
			progressCheckCount++;
			if (progressCheckCount % 60 == 0) // Log every ~1 second at 60fps
			{
				SceneTransitionLogger.LogTransitionStep("LOAD_PROGRESS", $"Progress: {op.progress:F2} (check #{progressCheckCount})");
			}

			// Check for potential freeze
			if (progressCheckCount > 600) // More than 10 seconds at 60fps
			{
				SceneTransitionLogger.LogPotentialFreeze($"Scene loading taking too long - Progress: {op.progress:F2}, Checks: {progressCheckCount}");
			}

			yield return null;
		}

		SceneTransitionLogger.LogTransitionStep("LOAD_PROGRESS_COMPLETE", $"Scene load progress reached {op.progress:F2} after {progressCheckCount} checks");

		if (autoActivateOnReady)
		{
			SceneTransitionLogger.LogTransitionStep("AUTO_ACTIVATE_PATH", "Taking auto-activate path");

			// Ensure minimum display time
			float waitStart = Time.unscaledTime;
			while (Time.unscaledTime - shownStart < minDisplayTime)
			{
				yield return null;
			}
			SceneTransitionLogger.LogTransitionStep("MIN_DISPLAY_TIME_COMPLETE", $"Minimum display time satisfied ({Time.unscaledTime - waitStart:F2}s)");

			// Fade out before activating scene
			SceneTransitionLogger.LogTransitionStep("FADE_OUT_START", "Starting fade-out animation");
			yield return StartCoroutine(FadeOut());
			SceneTransitionLogger.LogTransitionStep("FADE_OUT_COMPLETE", "Fade-out animation completed");

			// Activate scene - this is another critical point
			SceneTransitionLogger.LogPotentialFreeze("About to activate scene");
			op.allowSceneActivation = true;
			SceneTransitionLogger.LogTransitionStep("SCENE_ACTIVATION_ENABLED", "Scene activation enabled");

			int activationWaitCount = 0;
			while (!op.isDone)
			{
				activationWaitCount++;
				if (activationWaitCount > 300) // More than 5 seconds at 60fps
				{
					SceneTransitionLogger.LogPotentialFreeze($"Scene activation taking too long - Wait count: {activationWaitCount}");
				}
				yield return null;
			}
			SceneTransitionLogger.LogTransitionStep("SCENE_ACTIVATION_COMPLETE", $"Scene activation completed after {activationWaitCount} frames");

			// Clean up
			SceneTransitionLogger.LogTransitionStep("CLEANUP_START", "Starting BootLoader cleanup");
			SceneTransitionLogger.LogSceneTransitionEnd(target);
			Destroy(gameObject);
		}
		else
		{
			_loadingText.text = "Press any key to continue";
			// Wait for user input
			while (!Input.anyKeyDown)
			{
				yield return null;
			}
			
			// Fade out before activating scene
			yield return StartCoroutine(FadeOut());
			
			op.allowSceneActivation = true;
			while (!op.isDone) { yield return null; }
			
			// Clean up
			Destroy(gameObject);
		}
	}

	private IEnumerator FadeIn()
	{
		float elapsed = 0f;
		while (elapsed < fadeInDuration)
		{
			elapsed += Time.unscaledDeltaTime;
			float t = elapsed / fadeInDuration;
			_group.alpha = Mathf.Lerp(0f, 1f, t);
			yield return null;
		}
		_group.alpha = 1f;
	}

	private IEnumerator FadeOut()
	{
		float elapsed = 0f;
		float startAlpha = _group.alpha;
		while (elapsed < fadeOutDuration)
		{
			elapsed += Time.unscaledDeltaTime;
			float t = elapsed / fadeOutDuration;
			_group.alpha = Mathf.Lerp(startAlpha, 0f, t);
			yield return null;
		}
		_group.alpha = 0f;
	}

	private void Update()
	{
		if (_loadingText != null)
		{
			// Animate loading dots
			float time = Time.unscaledTime;
			int dotCount = Mathf.FloorToInt(time * 2f) % 4;
			string dots = new string('.', dotCount);
			_loadingText.text = "Initializing Persistence Engine" + dots;
		}
	}

	private IEnumerator WaitForAnyKeyThenQuit()
	{
		_loadingText.text += "\nPress any key to quit";
		while (!Input.anyKeyDown)
		{
			yield return null;
		}
#if UNITY_EDITOR
		EditorApplication.isPlaying = false;
#else
		Application.Quit();
#endif
	}

	private void OnDestroy()
	{
		// Restore FPS settings
		if (capFpsDuringBoot)
		{
			QualitySettings.vSyncCount = _prevVSync;
			Application.targetFrameRate = _prevTargetFps;
		}
	}
}


