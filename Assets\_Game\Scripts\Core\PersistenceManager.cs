using UnityEngine;
using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Collections.Generic;  // Add this for List<T>
using System.Linq;
using System.Collections;
using KinematicCharacterController.FPS; // Add this for FPS character controller access
using KinematicCharacterController;

public class PersistenceManager : MonoBehaviour
{
    // Global restoration flag used to gate movers/attachments while persistence is restoring
    public static bool IsRestoring { get; private set; } = false;

    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    private static void BootstrapRestorationFlag()
    {
        // Assume we're restoring at startup until PersistenceManager marks complete
        IsRestoring = true;
    }

    // Add a debug logging flag
    private static bool verboseLogging = false;

    private void Log(string message)
    {
        if (verboseLogging)
        {
            Debug.Log(message);
        }
    }

    // Public API to begin deferred initialization when entering gameplay
    public void InitializeForGameplay()
    {
        SafeLogTransitionStep("PERSISTENCE_INIT_FOR_GAMEPLAY", "PersistenceManager.InitializeForGameplay called");
        if (!deferInitializationUntilGameplay)
        {
            // Nothing to do, we already initialized in Start()
            SafeLogTransitionStep("PERSISTENCE_ALREADY_INIT", "Persistence already initialized in Start()");
            return;
        }
        if (_initializationCompleted || _initializationStarted)
        {
            SafeLogTransitionStep("PERSISTENCE_INIT_ALREADY_RUNNING", $"Initialization already running/complete - Started: {_initializationStarted}, Completed: {_initializationCompleted}");
            return;
        }
        SafeLogTransitionStep("PERSISTENCE_DEFERRED_INIT_START", "Starting deferred initialization routine");
        StartCoroutine(DeferredInitializationRoutine());
    }

    // Safe logging method that won't crash if SceneTransitionLogger isn't available
    private void SafeLogTransitionStep(string step, string details)
    {
        try
        {
            // Use reflection to safely call SceneTransitionLogger if it exists
            var loggerType = System.Type.GetType("SceneTransitionLogger");
            if (loggerType != null)
            {
                var method = loggerType.GetMethod("LogTransitionStep", System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                if (method != null)
                {
                    method.Invoke(null, new object[] { step, details });
                    return;
                }
            }
        }
        catch (System.Exception)
        {
            // Ignore reflection errors
        }

        // Fallback to regular debug log
        Debug.Log($"[PersistenceTransition] {step}: {details}");
    }

    // Public property to check if initialization is complete
    public bool IsInitializationComplete => _initializationCompleted;

    // Perform heavy restore/registration in steps over multiple frames
    private IEnumerator DeferredInitializationRoutine()
    {
        _initializationStarted = true;
        Debug.Log("[PlayerProgression] Deferred initialization starting...");

        // Reacquire minimal references if missing
        if (playerTransform == null)
        {
            var foundPlayerController = FindObjectOfType<FPSCharacterController>();
            if (foundPlayerController != null)
            {
                playerController = foundPlayerController;
                playerTransform = foundPlayerController.transform;
            }
        }
        yield return null;

        // Ensure world items container exists
        if (worldItemsContainer == null)
        {
            var manager = FindObjectOfType<WorldItemManager>();
            if (manager != null && manager.ItemsContainer != null)
            {
                worldItemsContainer = manager.ItemsContainer;
            }
            else
            {
                GameObject container = new GameObject("World_Items_Container");
                worldItemsContainer = container.transform;
            }
        }
        worldItemsContainerInitialized = true;
        yield return null;

        // Register existing items first to avoid duplicates
        RegisterExistingWorldItems();
        yield return null;

        // Load pickup log for latest state
        LoadAndApplyPickupLog();
        yield return null;

        // Restore inventory/equipment
        RestoreInventoryAndEquipment();
        yield return null;

        // Restore world items
        RestoreWorldItems();
        yield return null;

        // Restore heavy objects
        RestoreHeavyObjects();
        yield return null;

        // Restore kinematic platforms
        RestoreKinematicPlatforms();
        yield return null;

        // Register for item events
        RegisterForItemEvents();
        yield return null;

        // Save initial state after restore
        ConsolidateSaves();
        Debug.Log("[PlayerProgression] Deferred initialization finished");

        // Ensure we save on quit and cleanup memory
        Application.quitting += SaveProgressionData;
        Application.quitting += CleanupMemoryLeaks;

        // Apply saved transform at end-of-frame and let restoration window close via existing logic
        StartCoroutine(ApplySavedPlayerTransformEndOfFrame());

        _initializationCompleted = true;
        Debug.Log("[PlayerProgression] Set _initializationCompleted = true");
    }

    private void LogWarning(string message)
    {
        Debug.LogWarning(message);
    }

    private void LogError(string message)
    {
        Debug.LogError(message);
    }

    private const string PROGRESSION_SAVE_KEY = "player_progression";
    private const string SAVE_FILE_NAME = "player_progression.dat";
    private const string PICKUP_LOG_FILE_NAME = "pickup_log.dat"; // New file for pickup events
    private const string ENCRYPTION_KEY = "BLAME_GAME_SECURE_KEY_2024"; // Change this in production
    private const int DEFAULT_STASH_ROWS = 3;
    private const float MAIN_SAVE_INTERVAL = 300f; // 5 minutes between full saves
    private float timeSinceLastFullSave = 0f;
    
    // Player position tracking for real-time saves
    private const float PLAYER_POSITION_SAVE_INTERVAL = 0.25f; // Save player position every 0.25 seconds for force quit protection
    private float timeSinceLastPlayerPositionSave = 0f;
    private Vector3 lastSavedPlayerPosition = Vector3.zero;
    private Transform playerTransform;
    private FPSCharacterController playerController;
    private RagdollTumbleSystem ragdollTumbleSystem;
    private bool isQuittingApp = false;
    private float lastRestorationTime = -1f;
    private Vector3 lastCommittedSavedPosition = Vector3.zero;
    private string lastCommittedPositionSource = "";
    
    // Item velocity tracking
    private const float ITEM_VELOCITY_SAVE_INTERVAL = 2f; // Save item velocities every 2 seconds
    private float timeSinceLastVelocitySave = 0f;

    // Thread-safe collection for background thread error messages
    private List<string> pickupLogErrorMessages = new List<string>();

    [SerializeField] private PlayerStatus playerStatus;
    private CurrencyManager currencyManager;
    [SerializeField] private EquipmentManager equipmentManager;  // Reference to the player's equipment manager
    [SerializeField] private InvItemContainer playerInventory;   // Reference to the player's main inventory
    [SerializeField] private StashSystem stashSystem;            // Reference to the stash system
    
    // World item tracking
    [SerializeField] private Transform worldItemsContainer; // Optional parent for spawned items
    private bool worldItemsContainerInitialized = false;

    // Optimization settings for large scenes
    [Header("Performance Settings")]
    [SerializeField] private int batchSize = 100; // Number of items to process in each batch
    [SerializeField] private bool enableBatchProcessing = true; // Whether to use batch processing
    [SerializeField] private bool optimizeForLargeScenes = true; // Enable optimizations for large scenes

    // Initialization control
    [Header("Initialization Settings")]
    [SerializeField] private bool deferInitializationUntilGameplay = true; // Defer heavy restore/registration until gameplay starts
    private bool _initializationStarted = false;
    private bool _initializationCompleted = false;

    // Cache to avoid GC allocations
    private List<string> tempItemKeys = new List<string>(1000);
    private List<WorldItemData> tempItemsToRemove = new List<WorldItemData>(100);

    // Object pool for WorldItemData to reduce garbage collection
    private Queue<WorldItemData> worldItemDataPool = new Queue<WorldItemData>(100);

    // Cache for ItemUniqueId components to reduce GetComponent calls
    private Dictionary<string, ItemUniqueId> itemUniqueIdCache = new Dictionary<string, ItemUniqueId>();

    // Optimize pickup event handling with object pooling and deferred operations
    private Queue<PickupLogEntry> pickupLogEntryPool = new Queue<PickupLogEntry>(20);
    private List<Action> deferredPickupOperations = new List<Action>(10);

    // Track platforms to pause during item restoration only
    private List<KinematicPlatform> _platformsPausedForItemRestore = new List<KinematicPlatform>(64);

    // Cache frequently accessed components
    private FPSCharacterCamera _cachedFpsCamera;

    [System.Serializable]
    public class ProgressionData
    {
        public int stashRows = DEFAULT_STASH_ROWS;
        public int playerMoney = 0;
        public string saveVersion = "1.0"; // For future compatibility
        
        // New serialized inventory data
        public string playerInventoryData = "{}";
        public List<EquipmentSlotData> equipmentData = new List<EquipmentSlotData>();
        
        // World items data
        public List<WorldItemData> worldItems = new List<WorldItemData>();
        public List<string> pickedUpItemIds = new List<string>(); // Store IDs of items that have been picked up
        // Heavy objects data
        public List<HeavyObjectData> heavyObjects = new List<HeavyObjectData>();
        
        // Save versioning
        public int saveFormatVersion = 1; // Incremented when save structure changes
        public string gameVersion = "1.0.0"; // Current game version
        
        // Player energy level data
        public float playerCurrentEnergy = 100f;
        
        // Player position data for real-time saving
        public Vector3 playerPosition = Vector3.zero;
        public Quaternion playerRotation = Quaternion.identity;
        public string playerSceneName = "";
        public float playerPositionTimestamp = 0f;
        
        // Camera rotation data (separate from player rotation for FPS)
        public float cameraRotationX = 0f; // Pitch
        public float cameraRotationY = 0f; // Yaw
        
        // Player velocity data for physics continuity
        public Vector3 playerVelocity = Vector3.zero;
        public Vector3 playerAngularVelocity = Vector3.zero;
        public bool playerIsGrounded = true;
        public float playerVelocityTimestamp = 0f;

        // Player falling state for fall damage continuity
        public bool playerWasFalling = false;
        public float playerFallStartHeight = 0f;

        // Tool selection data
        public string selectedToolName = "Hands"; // Name of the currently selected tool
        public string selectedToolItemName = ""; // Name of the original item if it's not hands

        // Kinematic platforms persistence
        public List<KinematicPlatformData> kinematicPlatforms = new List<KinematicPlatformData>();
        // Player riding state on platform
        public bool playerWasOnPlatform = false;
        public string playerPlatformId = "";
        public Vector3 playerLocalOffsetOnPlatform = Vector3.zero;
    }
    
    [System.Serializable]
    public class EquipmentSlotData
    {
        public EquipmentSlotType slotType;
        public string equippedItemName;
        public string containerData = "{}";
        public float armorStoredEnergy = 0f; // Energy stored in armor pieces
    }
    
    [System.Serializable]
    public class WorldItemData
    {
        public string id; // Unique identifier for the item
        public string itemName; // Name of the item from ItemDatabase
        public int quantity; // How many items in the stack
        public bool wasDropped; // Whether this was player-dropped vs placed in level
        public bool wasStabilized; // Whether this item was moved from its original position
        public Vector3 position;
        public Quaternion rotation;
        public string containerData = "{}"; // For storage items like bags
        public float armorStoredEnergy = 0f; // Energy stored in armor pieces

        // Physics data for velocity persistence
        public Vector3 velocity = Vector3.zero;
        public Vector3 angularVelocity = Vector3.zero;
        public bool isKinematic = false;
        public float velocityTimestamp = 0f; // When velocity was last captured
    }

    // New class for pickup log entries
    [System.Serializable]
    public class PickupLogEntry
    {
        public string id; // Item's unique ID
        public string itemName; // Name of the item
        public int quantity; // Quantity picked up
        public long timestamp; // When the pickup occurred
        public bool isPickup; // True for pickup, false for drop (if we need to track drops too)

        public PickupLogEntry(string id, string itemName, int quantity, bool isPickup = true)
        {
            this.id = id;
            this.itemName = itemName;
            this.quantity = quantity;
            this.timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            this.isPickup = isPickup;
        }
    }

    // Container for multiple log entries
    [System.Serializable]
    public class PickupLog
    {
        public List<PickupLogEntry> entries = new List<PickupLogEntry>();
    }

    private ProgressionData progressionData;
    private PickupLog pickupLog = new PickupLog();
    private Dictionary<string, InvItemPickup> activeWorldItems = new Dictionary<string, InvItemPickup>();
    private bool pickupLogDirty = false; // Tracks if we have unsaved log entries
    
    // Heavy object tracking
    private Dictionary<string, HeavyObjectManager> activeHeavyObjects = new Dictionary<string, HeavyObjectManager>();
    private List<string> tempHeavyObjectKeys = new List<string>(100);
    private List<HeavyObjectData> tempHeavyObjectsToRemove = new List<HeavyObjectData>(50);

    // Kinematic platforms tracking
    private Dictionary<string, KinematicPlatformPersistence> activePlatforms = new Dictionary<string, KinematicPlatformPersistence>();
    private List<KinematicPlatformData> tempPlatformsToRemove = new List<KinematicPlatformData>(50);
    
    // Money property for the shop system - updated to use CurrencyManager when available
    public int PlayerMoney
    {
        get { 
            // Always use CurrencyManager as the source of truth
            if (currencyManager != null) {
                return currencyManager.CurrentCurrency;
            }
            // If CurrencyManager doesn't exist (which shouldn't happen), fall back to saved data
            return progressionData?.playerMoney ?? 0; 
        }
        set { 
            if (progressionData != null) 
            {
                // Always store the value in progression data for persistence
                progressionData.playerMoney = value;
                
                // Always update CurrencyManager
                if (currencyManager != null) {
                    currencyManager.SetCurrency(value);
                    Debug.Log($"[PlayerProgression] PlayerMoney property set to {value} via CurrencyManager");
                }
                else {
                    Debug.LogError("[PlayerProgression] CurrencyManager not found when trying to set currency value!");
                    // Create a CurrencyManager if missing
                    GameObject cmObj = new GameObject("CurrencyManager");
                    currencyManager = cmObj.AddComponent<CurrencyManager>();
                    currencyManager.SetCurrency(value);
                }
            }
        }
    }

    // Static instance for easy access in single-player
    public static PersistenceManager Instance { get; private set; }

    private string SaveFilePath => Path.Combine(Application.persistentDataPath, SAVE_FILE_NAME);
    private string PickupLogFilePath => Path.Combine(Application.persistentDataPath, PICKUP_LOG_FILE_NAME);

    private void Awake()
    {
        // Simple singleton pattern for single-player
        if (Instance == null)
        {
            Instance = this;
            LoadProgressionData();
            // Ensure restoration gate is engaged as early as possible
            IsRestoring = true;
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        // Keep restoration gate on during the whole Start() initialization
        IsRestoring = true;
        // Find all dependencies first
        if (playerStatus == null) {
            playerStatus = FindObjectOfType<PlayerStatus>();
            if (playerStatus == null) {
                LogError("[PlayerProgression] Could not find PlayerStatus - currency functions will not work properly!");
            }
        }
        
        // Find the player transform and controller for position and velocity tracking
        if (playerTransform == null) {
            // Try to find the player transform through various methods
            var foundPlayerController = FindObjectOfType<FPSCharacterController>();
            if (foundPlayerController != null) {
                playerController = foundPlayerController;
                playerTransform = foundPlayerController.transform;
            } else {
                var playerManager = FindObjectOfType<FPSPlayerManager>();
                if (playerManager != null) {
                    playerTransform = playerManager.transform;
                    // Try to get the character controller from the player manager
                    if (playerManager.Character != null) {
                        playerController = playerManager.Character;
                    }
                } else {
                    // Last resort - find any object with "Player" in the name
                    GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
                    if (playerObj != null) {
                        playerTransform = playerObj.transform;
                        playerController = playerObj.GetComponent<FPSCharacterController>();
                    }
                }
            }
            
            if (playerTransform != null) {
                Log("[PlayerProgression] Found player transform for position tracking");
                if (playerController != null) {
                    Log("[PlayerProgression] Found player controller for velocity tracking");
                } else {
                    LogWarning("[PlayerProgression] Could not find player controller - velocity tracking disabled");
                }
                
                // Also try to find ragdoll tumble system for tumble-aware position saving
                if (ragdollTumbleSystem == null)
                {
                    ragdollTumbleSystem = FindObjectOfType<RagdollTumbleSystem>();
                    if (ragdollTumbleSystem != null)
                    {
                        Log("[PlayerProgression] Found RagdollTumbleSystem for tumble-aware position tracking");
                    }
                }
                
                // Restore player position and velocity now that we have the transform
                if (!deferInitializationUntilGameplay)
                {
                    RestorePlayerPosition();
                }
            } else {
                LogWarning("[PlayerProgression] Could not find player transform - real-time position saving disabled");
            }
        }
        
        // Find the CurrencyManager immediately
        currencyManager = CurrencyManager.Instance;
        if (currencyManager == null)
        {
            LogError("[PlayerProgression] CurrencyManager not found on Start! Creating one.");
            GameObject cmObj = new GameObject("CurrencyManager");
            currencyManager = cmObj.AddComponent<CurrencyManager>();
        }
        
        if (equipmentManager == null) {
            equipmentManager = FindObjectOfType<EquipmentManager>();
        }
        
        if (stashSystem == null) {
            stashSystem = FindObjectOfType<StashSystem>();
        }
        
        if (playerInventory == null && stashSystem != null) {
            playerInventory = stashSystem.GetStashContainer();
        }
        
        if (progressionData == null)
        {
            LoadProgressionData();
        }

        // Initialize player currency with high priority - ensure we wait for PlayerStatus to be ready
        StartCoroutine(InitializePlayerCurrencyDelayed());
        
        // Rest of initialization continues...
        
        if (playerStatus != null)
        {
            // Removed duplicate currency initialization
            
            // PlayerProgressionManager is now the sole authority - always schedule periodic saves
            InvokeRepeating("PeriodicFullSave", MAIN_SAVE_INTERVAL, MAIN_SAVE_INTERVAL);
            
            // Restore energy levels from save
            // Only restore current energy - max energy will be recalculated from equipped armor
            playerStatus.currentEnergy = progressionData.playerCurrentEnergy;
            Log($"[PlayerProgression] Restored player energy: {playerStatus.currentEnergy}/{playerStatus.maxEnergy}");
        }
        else
        {
            LogWarning("[PlayerProgression] PlayerStatus not found! Currency sync will not happen.");
        }
        
        // Initialize world items container if not set
        if (worldItemsContainer == null)
        {
            // Try to find an existing container in the scene
            var manager = FindObjectOfType<WorldItemManager>();
            if (manager != null && manager.ItemsContainer != null)
            {
                worldItemsContainer = manager.ItemsContainer;
                Log("[PlayerProgression] Found world items container from WorldItemManager");
            }
            else
            {
                // Create a new container if none exists
                GameObject container = new GameObject("World_Items_Container");
                worldItemsContainer = container.transform;
                Log("[PlayerProgression] Created new world items container");
            }
        }
        
        worldItemsContainerInitialized = true;
        
        if (!deferInitializationUntilGameplay)
        {
            // IMPORTANT: We need to register existing world items BEFORE restoring saved items
            // to prevent duplicates
            RegisterExistingWorldItems();
            
            // Load the pickup log to get the most up-to-date state
            LoadAndApplyPickupLog();
            
            // Now restore inventory, equipment, and saved items
            RestoreInventoryAndEquipment();
            RestoreWorldItems();
            // Restore heavy objects
            RestoreHeavyObjects();
            // Restore kinematic platforms
            RestoreKinematicPlatforms();
            
            // No global safety timeout here; release is handled deterministically when placement verifies
            
            // Register for item events
            RegisterForItemEvents();
            
            // PlayerProgressionManager is now the sole authority for saves - no GameFlowManager integration needed
            
            // Ensure we save on quit
            Application.quitting += SaveProgressionData;
            
            // Save initial state
            ConsolidateSaves();
            
            Log("[PlayerProgression] Startup complete (immediate init)");

            // Mark initialization flags for immediate path
            _initializationStarted = true;
            _initializationCompleted = true;

            // Apply saved position again at the very end of the first frame to override any controller resets
            StartCoroutine(ApplySavedPlayerTransformEndOfFrame());
        }
        else
        {
            Debug.Log("[PlayerProgression] Deferring heavy initialization until gameplay entry");
        }
    }

    // Coroutine to re-apply saved player transform after everything has initialised (end-of-frame)
    private IEnumerator ApplySavedPlayerTransformEndOfFrame()
    {
        // Wait until end of current frame so other systems (character controller, scene load callbacks, etc.) have run
        yield return new WaitForEndOfFrame();

        // Apply only once
        if (!playerPositionRestored)
        {
            RestorePlayerPosition();
            // Always finish restoration after a short delay since platform attachment is handled immediately
            StartCoroutine(FinishRestorationAfterFrames(2));
        }
        else
        {
            // Always finish restoration after a short delay
            StartCoroutine(FinishRestorationAfterFrames(2));
        }
    }

    private void Update()
    {
        // Skip all persistence timers and debug input while in main menu
        if (GameFlowManager.IsInMainMenu)
        {
            return;
        }

        // Process any error messages from background threads
        if (pickupLogErrorMessages.Count > 0)
        {
            lock(pickupLogErrorMessages)
            {
                foreach (var error in pickupLogErrorMessages)
                {
                    Debug.LogError(error);
                }
                pickupLogErrorMessages.Clear();
            }
        }
        
        // Debug functionality - press F8 to delete all saved world items
        if (Input.GetKeyDown(KeyCode.F8))
        {
            DeleteAllWorldItems();
        }
        
        // Debug functionality - press F9 to clear duplicates
        if (Input.GetKeyDown(KeyCode.F9))
        {
            Debug.Log("[PlayerProgression] Manual duplicate cleanup triggered");
            CleanupDuplicatesAndRestore();
        }
        
        // Debug functionality - press F12 to clean up messy items
        if (Input.GetKeyDown(KeyCode.F12))
        {
            Debug.Log("[PlayerProgression] Manual messy item cleanup triggered");
            CleanupMessyItems();
        }
        
        // Real-time player position saving
        timeSinceLastPlayerPositionSave += Time.deltaTime;
        if (timeSinceLastPlayerPositionSave >= PLAYER_POSITION_SAVE_INTERVAL)
        {
            SavePlayerPosition();
            timeSinceLastPlayerPositionSave = 0f;
        }
        
        // Real-time item velocity saving
        timeSinceLastVelocitySave += Time.deltaTime;
        if (timeSinceLastVelocitySave >= ITEM_VELOCITY_SAVE_INTERVAL)
        {
            SaveItemVelocities();
            timeSinceLastVelocitySave = 0f;
        }

        // Opportunistically save kinematic platform state for active movers
        if (progressionData != null && activePlatforms.Count > 0)
        {
            foreach (var kv in activePlatforms)
            {
                var plat = kv.Value;
                if (plat != null && plat.Platform != null && (plat.Platform.IsMoving || plat.Platform.IsWaiting))
                {
                    UpdateKinematicPlatformData(plat);
                }
            }
        }
        
        // Check if it's time for a full save
        timeSinceLastFullSave += Time.deltaTime;
        if (timeSinceLastFullSave >= MAIN_SAVE_INTERVAL)
        {
            ConsolidateSaves();
            timeSinceLastFullSave = 0f;
        }
        
        // If pickup log is dirty and it's been a while since our last save, write it to disk
        if (pickupLogDirty)
        {
            SavePickupLog();
            pickupLogDirty = false;
        }
    }

    private void LateUpdate()
    {
        // Process any deferred pickup operations at the end of the frame
        if (deferredPickupOperations.Count > 0)
        {
            foreach (var operation in deferredPickupOperations)
            {
                operation.Invoke();
            }
            deferredPickupOperations.Clear();
        }
        
        // Similar logic to your Update, but only for pickup log saving
        if (pickupLogDirty)
        {
            SavePickupLog();
            pickupLogDirty = false;
        }
    }

    private void OnDisable()
    {
        // Persist everything when the manager is being disabled (e.g., scene unload)
        try
        {
            // Make sure latest states are captured
            SavePlayerPosition();
            SaveItemVelocities();
            SaveInventoryAndEquipment();
            // Force immediate processing to avoid losing items during shutdown
            SaveWorldItems(forceImmediate: true);
            SaveHeavyObjects();
            if (activePlatforms.Count > 0)
            {
                foreach (var kv in activePlatforms)
                {
                    if (kv.Value != null)
                    {
                        UpdateKinematicPlatformData(kv.Value);
                    }
                }
            }
            SaveProgressionDataOptimized();
        }
        catch (Exception e)
        {
            LogWarning($"[PlayerProgression] OnDisable save failed: {e.Message}");
        }
    }

    #region Heavy Object Persistence

    /// <summary>
    /// Register a heavy object for tracking
    /// </summary>
    public void RegisterHeavyObject(HeavyObjectManager heavyObject)
    {
        if (heavyObject == null) return;
        
        string id = heavyObject.UniqueId;
        
        // Add to active tracking
        activeHeavyObjects[id] = heavyObject;
        
        // Ensure list exists
        if (progressionData != null && progressionData.heavyObjects == null)
        {
            progressionData.heavyObjects = new List<HeavyObjectData>();
        }
        
        // Check if we have saved data for this object
        var savedData = progressionData?.heavyObjects.FirstOrDefault(h => h.id == id);
        if (savedData != null)
        {
            // Restore from saved data
            heavyObject.RestoreFromSaveData(savedData);
            Log($"[PlayerProgression] Restored heavy object {heavyObject.ObjectName} from save");
        }
        else
        {
            // Register a baseline entry immediately (like world items), so future updates persist
            UpdateHeavyObjectData(heavyObject);
        }
    }
    
    /// <summary>
    /// Unregister a heavy object from tracking
    /// </summary>
    public void UnregisterHeavyObject(HeavyObjectManager heavyObject)
    {
        if (heavyObject == null) return;
        
        string id = heavyObject.UniqueId;
        if (activeHeavyObjects.ContainsKey(id))
        {
            // Save final state before removing
            UpdateHeavyObjectData(heavyObject);
            activeHeavyObjects.Remove(id);
        }
    }
    
    /// <summary>
    /// Called when a heavy object has moved - saves immediately for moving objects
    /// </summary>
    public void OnHeavyObjectMoved(HeavyObjectManager heavyObject)
    {
        if (heavyObject == null) return;
        // Ignore movement events during or immediately after restoration window
        if (IsRestoring) return;
        if (lastRestorationTime > 0 && Time.time - lastRestorationTime < 1.0f) return;
        
        // For actively moving objects, save immediately in real-time
        if (heavyObject.IsCurrentlyMoving)
        {
            UpdateHeavyObjectData(heavyObject);
        }
        else
        {
            // For objects that have stopped, just update the data
            UpdateHeavyObjectData(heavyObject);
        }
    }
    
    /// <summary>
    /// Update or add heavy object data to save
    /// </summary>
    private void UpdateHeavyObjectData(HeavyObjectManager heavyObject)
    {
        if (progressionData == null || heavyObject == null) return;
        
        if (progressionData.heavyObjects == null)
        {
            progressionData.heavyObjects = new List<HeavyObjectData>();
        }
        
        string id = heavyObject.UniqueId;
        
        // Find existing entry or create new
        HeavyObjectData data = progressionData.heavyObjects.FirstOrDefault(h => h.id == id);
        
        if (data == null)
        {
            // Create new entry
            data = heavyObject.GetSaveData();
            progressionData.heavyObjects.Add(data);
            Log($"[PlayerProgression] Added heavy object to save: {heavyObject.ObjectName}");
        }
        else
        {
            // Update existing entry
            var newData = heavyObject.GetSaveData();
            
            // Update the existing data object (do not override physics config)
            data.objectName = newData.objectName;
            data.category = newData.category;
            data.position = newData.position;
            data.rotation = newData.rotation;
            data.scale = newData.scale;
            data.hasBeenMoved = newData.hasBeenMoved;
            data.isActive = newData.isActive;
            data.velocity = newData.velocity;
            data.angularVelocity = newData.angularVelocity;
            // Intentionally skip: isKinematic, useGravity, mass, drag, angularDrag
        }
    }
    
    /// <summary>
    /// Save all heavy objects - prioritizes actively moving objects for real-time accuracy
    /// </summary>
    private void SaveHeavyObjects()
    {
        if (progressionData == null) return;
        // Do not save heavy objects while restoration is in progress to avoid clobbering restored states
        if (IsRestoring) return;
        
        int updatedCount = 0;
        
        // Save all active heavy objects, like world items
        foreach (var kvp in activeHeavyObjects)
        {
            var heavyObject = kvp.Value;
            if (heavyObject == null) continue;
            UpdateHeavyObjectData(heavyObject);
            updatedCount++;
        }
        
        // Do not clean up heavy object entries here. Unlike world items, these may be
        // destroyed during scene unload/orderly shutdown before we save. Cleaning up now
        // can remove valid saves. We keep entries and allow restore by id on next load.
        
        if (verboseLogging && updatedCount > 0)
        {
            Log($"[PlayerProgression] Saved {updatedCount} heavy objects");
        }
    }
    
    /// <summary>
    /// Clean up heavy object data for objects that no longer exist
    /// </summary>
    private void CleanupHeavyObjectData()
    {
        if (progressionData == null || progressionData.heavyObjects == null) return;
        
        tempHeavyObjectsToRemove.Clear();
        
        foreach (var data in progressionData.heavyObjects)
        {
            if (!activeHeavyObjects.ContainsKey(data.id))
            {
                tempHeavyObjectsToRemove.Add(data);
            }
        }
        
        foreach (var data in tempHeavyObjectsToRemove)
        {
            progressionData.heavyObjects.Remove(data);
        }
    }
    
    /// <summary>
    /// Restore all heavy objects from save
    /// </summary>
    private void RestoreHeavyObjects()
    {
        if (progressionData == null || progressionData.heavyObjects == null) return;
        
        int restoredCount = 0;
        
        // Find all heavy objects already in the scene
        var sceneHeavyObjects = FindObjectsOfType<HeavyObjectManager>();
        
        foreach (var heavyObject in sceneHeavyObjects)
        {
            if (heavyObject == null) continue;
            
            // Register it (this will also restore its state if saved)
            RegisterHeavyObject(heavyObject);
            restoredCount++;
        }
        
        if (verboseLogging && restoredCount > 0)
        {
            Log($"[PlayerProgression] Found and restored {restoredCount} heavy objects in scene");
        }
        
        // Optional: Log any saved objects that weren't found in scene
        if (verboseLogging)
        {
            foreach (var savedData in progressionData.heavyObjects)
            {
                if (!activeHeavyObjects.ContainsKey(savedData.id))
                {
                    Log($"[PlayerProgression] Saved heavy object not found in scene: {savedData.objectName} ({savedData.id})");
                }
            }
        }
    }

    // Try to locate an existing saved HeavyObjectData that likely corresponds to this object,
    // even if the computed UniqueId changed across sessions due to small transform/name changes.
    private HeavyObjectData TryFindMatchingSavedHeavyObject(HeavyObjectManager heavyObject)
    {
        if (progressionData == null || progressionData.heavyObjects == null || heavyObject == null) return null;

        string sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        Vector3 here = heavyObject.transform.position;

        // Narrow down candidates by scene (id prefix) only
        var candidates = progressionData.heavyObjects
            .Where(h => h != null
                        && !string.IsNullOrEmpty(h.id)
                        && h.id.StartsWith($"HO_{sceneName}_"))
            .ToList();

        if (candidates.Count == 0) return null;

        // Select the closest by saved position (no hard threshold; always pick a best match)
        HeavyObjectData best = null;
        float bestDist = float.MaxValue;
        foreach (var c in candidates)
        {
            float d = (c.position - here).sqrMagnitude;
            if (d < bestDist)
            {
                bestDist = d;
                best = c;
            }
        }

        return best;
    }
    
    #endregion

    #region Kinematic Platform Persistence

    [System.Serializable]
    public class KinematicPlatformData
    {
        public string id;
        public string objectName;
        public string sceneName;

        public int currentWaypointIndex;
        public int directionMultiplier;
        public bool isMoving;
        public bool isWaiting;
        public float waitTimeRemaining;
        public float currentTime;
        public float journeyDuration;
        public Vector3 startPosition;
        public Vector3 targetPosition;
        public KinematicPlatform.MotionCurveType motionType;
        public float curvePower;
        public float moveSpeed;
        public bool isLooping;

        // For quick restore position if needed
        public Vector3 currentWorldPosition;

        // NEW: Track all passengers saved on this platform
        public List<PassengerData> passengers = new List<PassengerData>();

        // Remember if the platform was explicitly paused by player button
        public bool pausedByButton = false;
    }

    [System.Serializable]
    public class PassengerData
    {
        public string id; // Unique identifier (player or object instance id)
        public PassengerType type; // Player, Item, Other
        public Vector3 localPosition; // Relative to platform
        public Quaternion localRotation; // Relative to platform
        public string itemName; // For items, database name
        public Vector3 velocity; // At save time
        public Vector3 angularVelocity; // At save time
        public bool wasKinematic; // For rigidbodies
        public RigidbodyConstraints originalConstraints; // Original constraints for restore
    }

    public enum PassengerType
    {
        Player,
        Item,
        Other
    }

    // Helper class for temporary parenting of rigidbodies during restore
    private class RBRestoreInfo
    {
        public Transform parent;
        public bool wasKinematic;
        public RigidbodyConstraints constraints;
    }
    
    // Public access for platform attachment system
    public ProgressionData GetProgressionData() => progressionData;

    public void RegisterKinematicPlatform(KinematicPlatformPersistence platform)
    {
        if (platform == null) return;
        string id = platform.UniqueId;
        activePlatforms[id] = platform;

        if (progressionData != null && progressionData.kinematicPlatforms == null)
        {
            progressionData.kinematicPlatforms = new List<KinematicPlatformData>();
        }

        var saved = progressionData?.kinematicPlatforms.FirstOrDefault(p => p.id == id);
        if (saved != null)
        {
            platform.RestoreFromSaveData(saved);
            // If player was on this platform when saved, attach immediately
            TryAttachPlayerToPlatformIfSaved(platform);
        }
        else
        {
            // Try to migrate from older ID schemes (including sibling index format)
            var sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            var candidates = progressionData?.kinematicPlatforms
                ?.Where(p => p.sceneName == sceneName && (
                    p.objectName == platform.ObjectName || 
                    p.id.Contains(platform.ObjectName) ||
                    p.id.Replace("[" + p.id.Split('[').LastOrDefault()?.Split(']').FirstOrDefault() + "]", "") == id
                ))
                ?.ToList();
            if (candidates != null && candidates.Count > 0)
            {
                Vector3 here = platform.transform.position;
                var best = candidates
                    .OrderBy(p => (p.currentWorldPosition - here).sqrMagnitude)
                    .First();
                // Adopt this saved entry under the current unique id
                string oldId = best.id;
                best.id = id;
                platform.RestoreFromSaveData(best);
                // If the player save referenced the old id, update it to the new one
                if (progressionData.playerWasOnPlatform && progressionData.playerPlatformId == oldId)
                {
                    progressionData.playerPlatformId = id;
                }
                TryAttachPlayerToPlatformIfSaved(platform);
            }
            else
            {
                UpdateKinematicPlatformData(platform);
            }
        }
        
        // CRITICAL: Do not release platform hold here - only release after player is placed
        // The platform must stay frozen until TryAttachPlayerToPlatformIfSaved completes
    }

    public void UnregisterKinematicPlatform(KinematicPlatformPersistence platform)
    {
        if (platform == null) return;
        string id = platform.UniqueId;
        if (activePlatforms.ContainsKey(id))
        {
            UpdateKinematicPlatformData(platform);
            activePlatforms.Remove(id);
        }
    }

    public void OnKinematicPlatformStateChanged(KinematicPlatformPersistence platform)
    {
        if (platform == null) return;
        UpdateKinematicPlatformData(platform);
    }

    private void UpdateKinematicPlatformData(KinematicPlatformPersistence platform)
    {
        if (progressionData == null || platform == null) return;
        if (progressionData.kinematicPlatforms == null)
        {
            progressionData.kinematicPlatforms = new List<KinematicPlatformData>();
        }

        string id = platform.UniqueId;
        var existing = progressionData.kinematicPlatforms.FirstOrDefault(p => p.id == id);
        KinematicPlatformData newData = platform.GetSaveData();
        if (existing == null)
        {
            progressionData.kinematicPlatforms.Add(newData);
        }
        else
        {
            // Overwrite fields in-place to keep reference
            existing.objectName = newData.objectName;
            existing.sceneName = newData.sceneName;
            existing.currentWaypointIndex = newData.currentWaypointIndex;
            existing.directionMultiplier = newData.directionMultiplier;
            existing.isMoving = newData.isMoving;
            existing.isWaiting = newData.isWaiting;
            existing.waitTimeRemaining = newData.waitTimeRemaining;
            existing.currentTime = newData.currentTime;
            existing.journeyDuration = newData.journeyDuration;
            existing.startPosition = newData.startPosition;
            existing.targetPosition = newData.targetPosition;
            existing.motionType = newData.motionType;
            existing.curvePower = newData.curvePower;
            existing.moveSpeed = newData.moveSpeed;
            existing.isLooping = newData.isLooping;
            existing.currentWorldPosition = newData.currentWorldPosition;
            existing.pausedByButton = newData.pausedByButton;
        }
    }

    private void RestoreKinematicPlatforms()
    {
        if (progressionData == null || progressionData.kinematicPlatforms == null) return;

        var scenePlatforms = FindObjectsByType<KinematicPlatformPersistence>(FindObjectsSortMode.None);
        foreach (var platform in scenePlatforms)
        {
            RegisterKinematicPlatform(platform);
        }
    }

    private void TryAttachPlayerToPlatformIfSaved(KinematicPlatformPersistence platform)
    {
        if (progressionData == null || !progressionData.playerWasOnPlatform) return;
        if (platform == null) return;
        if (progressionData.playerPlatformId != platform.UniqueId) return;
        if (playerController == null || playerController.Motor == null) return;

        // CRITICAL: Immediately freeze platform before any calculations
        if (platform.Platform != null)
        {
            platform.Platform.SetHoldForPlayerRestore(true);
        }
        
        // Calculate world position based on the CURRENT platform position (which should be the restored position)
        Vector3 worldPos = platform.transform.TransformPoint(progressionData.playerLocalOffsetOnPlatform);
        
        // Place player immediately
        playerController.Motor.SetPosition(worldPos, true);
        playerController.Motor.BaseVelocity = Vector3.zero;
        
        // Force grounding/attachment
        var platformRb = platform.GetComponent<Rigidbody>();
        if (platformRb != null)
        {
            playerController.Motor.AttachedRigidbodyOverride = platformRb;
        }
        
        // Start verification coroutine to ensure stable placement before releasing platform
        StartCoroutine(VerifyPlayerPlacementAndRelease(platform, worldPos));
        
        Debug.Log($"[PlayerProgression] Placed player on platform {platform.UniqueId} at {worldPos}");
    }
    
    private IEnumerator VerifyPlayerPlacementAndRelease(KinematicPlatformPersistence platformPersist, Vector3 expectedPosition)
    {
        if (platformPersist == null || playerController == null) yield break;
        
        var platform = platformPersist.Platform;
        if (platform == null) yield break;
        
        // Wait just 2 frames to ensure placement, then release immediately
        yield return null;
        yield return null;
        
        // Release platform immediately - let the passenger zone handle normal attachment
        if (platform != null)
        {
            platform.SetHoldForPlayerRestore(false);
            Debug.Log($"[PlayerProgression] Released platform hold for {platformPersist.UniqueId}");
        }
    }
    
    private IEnumerator ReleasePlatformHoldAfterDelay(KinematicPlatform platform, float delay)
    {
        yield return new WaitForSeconds(delay);
        if (platform != null)
        {
            platform.SetHoldForPlayerRestore(false);
        }
    }

    private void CleanupKinematicPlatformData()
    {
        if (progressionData == null || progressionData.kinematicPlatforms == null) return;
        tempPlatformsToRemove.Clear();
        foreach (var data in progressionData.kinematicPlatforms)
        {
            if (!activePlatforms.ContainsKey(data.id))
            {
                tempPlatformsToRemove.Add(data);
            }
        }
        foreach (var data in tempPlatformsToRemove)
        {
            progressionData.kinematicPlatforms.Remove(data);
        }
    }

    #endregion

    // Periodic full save (called via InvokeRepeating if no GameFlowManager exists)
    private void PeriodicFullSave()
    {
        ConsolidateSaves();
    }

    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
                    // Save energy levels when application pauses
        SavePlayerEnergyLevels();
        
        // Save player position immediately
        SavePlayerPosition();
        
        // Save heavy objects on pause
        SaveHeavyObjects();
        
        // On application pause, consolidate everything
        ConsolidateSaves();
            
            // Extra safety - save currency to emergency backup
            if (playerStatus != null)
            {
                PlayerPrefs.SetInt("emergency_currency_backup", playerStatus.Currency);
                PlayerPrefs.Save();
                Debug.Log($"[PlayerProgression] Created emergency currency backup on pause: {playerStatus.Currency}");
            }
        }
    }

    private void OnApplicationQuit()
    {
        isQuittingApp = true;
        // Save energy levels before quitting
        SavePlayerEnergyLevels();
        
        // Save player position immediately (log both potential sources for debugging)
        {
            Vector3 playerPosNow = playerTransform != null ? playerTransform.position : Vector3.zero;
            Vector3 ragdollPosNow = Vector3.zero;
            bool isTumblingNow = false;
            if (ragdollTumbleSystem == null) { ragdollTumbleSystem = FindObjectOfType<RagdollTumbleSystem>(); }
            if (ragdollTumbleSystem != null)
            {
                isTumblingNow = ragdollTumbleSystem.IsTumbling;
                if (ragdollTumbleSystem.ragdollObject != null && ragdollTumbleSystem.ragdollObject.activeInHierarchy)
                {
                    ragdollPosNow = ragdollTumbleSystem.ragdollObject.transform.position;
                }
            }
            if (verboseLogging)
            {
                Debug.Log($"[PlayerProgression] OnApplicationQuit - isTumbling={isTumblingNow}, playerPos={playerPosNow}, ragdollPos={ragdollPosNow}");
            }
        }
        SavePlayerPosition();
        
        // Make sure to save position of all active world items before quitting
        Debug.Log("[PlayerProgression] Application quitting - saving all world items");
        
        // Explicitly save all world items
        SaveWorldItems();
        // Also save heavy objects
        SaveHeavyObjects();
        
        // Consolidate all saves on quit
        ConsolidateSaves();
        
        // Extra safety - save currency to emergency backup
        if (playerStatus != null)
        {
            PlayerPrefs.SetInt("emergency_currency_backup", playerStatus.Currency);
            
            // Also save to a simple direct key as final fallback
            PlayerPrefs.SetInt("player_currency_last", playerStatus.Currency);
            PlayerPrefs.Save();
            Debug.Log($"[PlayerProgression] Created emergency currency backup on quit: {playerStatus.Currency}");
        }
    }
    
    #region World Item Persistence
    
    // Register for item pickup events
    private void RegisterForItemEvents()
    {
        // This could be done with a central event system
        // For now, we'll do this by polling during save
    }
    
    // Clean item spawning method similar to InvItemDropping
    private GameObject SpawnRestoredItem(Item item, Vector3 position, Quaternion rotation, InvItemDropping itemDropping)
    {
        if (item == null || itemDropping == null) return null;
        
        GameObject prefabToSpawn;
        
        // Use the same logic as InvItemDropping.SpawnItemPrefab
        if (item.WorldModelPrefab != null)
        {
            // Use the item's own world model prefab directly
            prefabToSpawn = item.WorldModelPrefab;
        }
        else
        {
            // Fallback to generic prefab from InvItemDropping
            prefabToSpawn = itemDropping.GetItemDropPrefab();
        }
        
        if (prefabToSpawn == null)
        {
            LogError($"[PlayerProgression] No prefab available for item: {item.itemName}");
            return null;
        }
        
        // Instantiate the prefab
        GameObject spawnedItem = Instantiate(prefabToSpawn, position, rotation);
        
        // Ensure required components (same as InvItemDropping)
        EnsureRequiredComponentsForRestoration(spawnedItem, item);
        
        // Remove any model swapper components - we want to use the actual prefab directly
        // This component might be added by the generic prefab but we don't need it for direct WorldModelPrefab usage
        var modelSwapper = spawnedItem.GetComponent("InvItemModelSwapper");
        if (modelSwapper != null)
        {
            Destroy(modelSwapper);
        }
        
        return spawnedItem;
    }
    
    // Ensure required components for restored items (similar to InvItemDropping)
    private void EnsureRequiredComponentsForRestoration(GameObject restoredItem, Item item)
    {
        // Make sure it has a Rigidbody
        Rigidbody rb = restoredItem.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = restoredItem.AddComponent<Rigidbody>();
            
            // Use default physics settings
            rb.mass = 1f;
            rb.linearDamping = 0.5f;
            rb.angularDamping = 0.2f;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
            rb.isKinematic = false;
            rb.useGravity = true;
        }
        
        // Make sure it has a collider
        Collider collider = restoredItem.GetComponent<Collider>();
        if (collider == null)
        {
            // Check if any child has a collider
            Collider[] childColliders = restoredItem.GetComponentsInChildren<Collider>();
            if (childColliders.Length == 0)
            {
                // Add a box collider as fallback
                BoxCollider boxCollider = restoredItem.AddComponent<BoxCollider>();
                boxCollider.size = new Vector3(0.5f, 0.5f, 0.5f);
                boxCollider.center = Vector3.zero;
            }
        }
        
        // Make sure it has InvItemPickup component
        InvItemPickup itemPickup = restoredItem.GetComponent<InvItemPickup>();
        if (itemPickup == null)
        {
            itemPickup = restoredItem.AddComponent<InvItemPickup>();
        }
        
        // Set the item immediately
        itemPickup.SetItem(item, 1); // Quantity will be set later
    }
    
    // Register existing world items from the scene
    private void RegisterExistingWorldItems()
    {
        if (!worldItemsContainerInitialized)
        {
            LogWarning("[PlayerProgression] World items container not initialized!");
            return;
        }
    
        // Find all items in the scene with InvItemPickup components
        var worldItems = FindObjectsOfType<InvItemPickup>();
        
        if (verboseLogging) {
            Log($"[PlayerProgression] Found {worldItems.Length} items with InvItemPickup component in scene");
        }
        
        // Create a list of pickup results so we don't modify during enumeration
        List<(string itemId, InvItemPickup itemPickup)> pickupResults = new List<(string, InvItemPickup)>();
        
        foreach (var itemPickup in worldItems)
        {
            // Skip if the item reference is null
            if (itemPickup.item == null)
            {
                LogWarning($"[PlayerProgression] Item pickup found with no item reference: {itemPickup.gameObject.name}");
                continue;
            }
            
            // Skip items that were already picked up according to save data
            string itemId = GetItemId(itemPickup);
            
            if (progressionData.pickedUpItemIds.Contains(itemId))
            {
                // Add to destruction list
                pickupResults.Add((itemId, itemPickup));
            }
            else
            {
                // Register this item for tracking
                RegisterWorldItem(itemPickup);
            }
        }
        
        // Process destruction after enumeration
        foreach (var result in pickupResults)
        {
            if (verboseLogging) {
                Log($"[PlayerProgression] Destroying previously picked up item: {result.itemId}");
            }
            Destroy(result.itemPickup.gameObject);
        }
        
        if (verboseLogging) {
            Log($"[PlayerProgression] Registered {activeWorldItems.Count} world items");
        }
    }
    
    // Get a unique ID for an item - this is the key to tracking items between sessions
    private string GetItemId(InvItemPickup itemPickup)
    {
        if (itemPickup == null) return string.Empty;
        
        // First check if this item already has a unique ID stored (for dropped items)
        ItemUniqueId idComponent;
        string itemKey = itemPickup.gameObject.GetInstanceID().ToString();
        
        // Check cache first
        if (itemUniqueIdCache.TryGetValue(itemKey, out idComponent))
        {
            // Cache hit
        }
        else
        {
            // Cache miss, get the component and cache it
            idComponent = itemPickup.GetComponent<ItemUniqueId>();
            if (idComponent != null)
            {
                itemUniqueIdCache[itemKey] = idComponent;
            }
        }
        
        if (idComponent != null && !string.IsNullOrEmpty(idComponent.UniqueId))
        {
            // If the item has a valid ID already (and was dropped by player or was previously moved), use it
            if (idComponent.WasPlayerDropped || idComponent.WasStabilized)
            {
                return idComponent.UniqueId;
            }
        }
        
        // For level-placed items, generate a deterministic ID based on prefab name + position
        // The key is to use a consistent ID format that doesn't change between sessions
        string objectId = itemPickup.gameObject.name;
        if (objectId.Contains("(Clone)"))
        {
            objectId = objectId.Replace("(Clone)", "");
        }
        
        // Create a stable ID using a consistent format
        // IMPORTANT: Use the stored InitialPosition if available to avoid ID changes when the item moves (e.g., on platforms)
        Vector3 initialPos = (idComponent != null && idComponent.InitialPosition != Vector3.zero)
            ? idComponent.InitialPosition
            : itemPickup.transform.position;
        string posString = $"{Mathf.Round(initialPos.x * 10)}_{Mathf.Round(initialPos.y * 10)}_{Mathf.Round(initialPos.z * 10)}";
        string stableId = $"{UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}_{objectId}_{posString}";
        
        // Add the ID component if it doesn't exist
        if (idComponent == null)
        {
            idComponent = itemPickup.gameObject.AddComponent<ItemUniqueId>();
            // Cache the new component
            itemUniqueIdCache[itemKey] = idComponent;
        }
        
        // Set the ID and store initial position
        idComponent.UniqueId = stableId;
        idComponent.InitialPosition = initialPos;
        
        if (verboseLogging) {
            Log($"[PlayerProgression] Generated stable ID {stableId} for {itemPickup.gameObject.name}");
        }
        
        return stableId;
    }
    
    // Register a world item for tracking - also ensures its current position is stored
    public void RegisterWorldItem(InvItemPickup itemPickup)
    {
        if (itemPickup == null || itemPickup.item == null) return;
        
        // Store the original position and rotation before any operations
        Vector3 originalPosition = itemPickup.transform.position;
        Quaternion originalRotation = itemPickup.transform.rotation;
        
        string id = GetItemId(itemPickup);
        
        // Don't register if already picked up
        if (progressionData.pickedUpItemIds.Contains(id)) return;
        
        // Get item's movement status using proper method
        bool hasMoved = itemPickup.HasMoved();
        
        // Get cached ItemUniqueId component
        string itemKey = itemPickup.gameObject.GetInstanceID().ToString();
        ItemUniqueId idComponent;
        
        if (!itemUniqueIdCache.TryGetValue(itemKey, out idComponent))
        {
            // Not in cache, get and cache it
            idComponent = itemPickup.GetComponent<ItemUniqueId>();
            if (idComponent != null)
            {
                itemUniqueIdCache[itemKey] = idComponent;
            }
        }
        
        bool isStabilized = idComponent != null && idComponent.WasStabilized;
        bool isPlayerDropped = idComponent != null && idComponent.WasPlayerDropped;
        
        // Update or add to active tracking dictionary
        activeWorldItems[id] = itemPickup;
        
        // Register with WorldItemManager for falling detection
        if (WorldItemManager.Instance != null)
        {
            WorldItemManager.Instance.RegisterItemForTracking(itemPickup);
        }
        
        // Only update position if:
        // 1. This is first registration (not in worldItems list yet)
        // 2. The item has been moved
        // 3. The item was player-dropped
        // 4. The item was previously moved and stabilized
        bool isNewItem = !progressionData.worldItems.Any(item => item.id == id);
        
        if (isNewItem || hasMoved || isPlayerDropped || isStabilized)
        {
            // Immediately save position in the world items data
            UpdateWorldItemPosition(id, itemPickup);
        }
        
        // Ensure the item hasn't been repositioned during registration
        itemPickup.transform.position = originalPosition;
        itemPickup.transform.rotation = originalRotation;
    }
    
    // Save world items state with batch processing for large scenes
    private void SaveWorldItems(bool forceImmediate = false)
    {
        if (progressionData == null) return;
        
        // Create a copy of the keys to avoid "Collection was modified" errors while iterating
        tempItemKeys.Clear();
        tempItemKeys.AddRange(activeWorldItems.Keys);
        
        // Track how many items were actually updated
        int updatedCount = 0;
        int totalItems = tempItemKeys.Count;
        
        // Use batch processing for large scenes
        if (!forceImmediate && enableBatchProcessing && totalItems > batchSize)
        {
            int batches = Mathf.CeilToInt((float)totalItems / batchSize);
            if (verboseLogging) {
                Log($"[PlayerProgression] Processing {totalItems} items in {batches} batches of {batchSize}");
            }
            
            // Process first batch immediately
            var (itemsProcessed, batchItems) = ProcessWorldItemBatch(0, batchSize, ref updatedCount);
            
            // Schedule remaining batches to be processed over multiple frames
            StartCoroutine(ProcessRemainingBatches(batches, batchSize, updatedCount));
            
            return; // We'll complete the save in the coroutine
        }
        
        // For smaller scenes, process all items immediately
        // Check all existing world items to ensure we have their latest positions
        foreach (var key in tempItemKeys)
        {
            if (!activeWorldItems.ContainsKey(key)) continue;
            
            var itemPickup = activeWorldItems[key];
            if (itemPickup == null || itemPickup.item == null) continue;
            
            // Only update position if the item has moved
            var idComponent = itemPickup.GetComponent<ItemUniqueId>();
            bool hasMoved = itemPickup.HasMoved(); // Use the getter method
            
            // Only update position data if this item was moved or is player-dropped
            if (hasMoved || (idComponent != null && idComponent.WasPlayerDropped) || (idComponent != null && idComponent.WasStabilized))
            {
                // Update the position data for this item
                UpdateWorldItemPosition(key, itemPickup, false); // Pass false to disable logging
                updatedCount++;
            }
        }
        
        // Create a list of items to remove instead of removing during enumeration
        tempItemsToRemove.Clear();
        foreach (var item in progressionData.worldItems)
        {
            if (progressionData.pickedUpItemIds.Contains(item.id) || !activeWorldItems.ContainsKey(item.id))
            {
                tempItemsToRemove.Add(item);
            }
        }
        
        // Now remove the items after enumeration is complete
        foreach (var item in tempItemsToRemove)
        {
            progressionData.worldItems.Remove(item);
        }
            
        if (verboseLogging && updatedCount > 0)
        {
            Log($"[PlayerProgression] Updated positions for {updatedCount} moved items out of {progressionData.worldItems.Count} total items");
        }
        
        // Save heavy objects alongside world items
        SaveHeavyObjects();
    }
    
    // Process a batch of world items for large scenes
    private (int itemsProcessed, List<KeyValuePair<string, float>> batchItems) ProcessWorldItemBatch(int batchIndex, int batchSize, ref int updatedCount)
    {
        int startIndex = batchIndex * batchSize;
        int endIndex = Mathf.Min(startIndex + batchSize, tempItemKeys.Count);
        int itemsProcessed = 0;
        
        // Cache camera for distance checks
        // Cache Camera.main reference once per call to avoid repeated static lookups
        Camera mainCam = Camera.main;
        Vector3 cameraPos = mainCam != null ? mainCam.transform.position : Vector3.zero;
        
        // Sort items by distance for this batch
        var batchItems = new List<KeyValuePair<string, float>>();
        for (int i = startIndex; i < endIndex; i++)
        {
            string key = tempItemKeys[i];
            if (!activeWorldItems.ContainsKey(key)) continue;
            
            var itemPickup = activeWorldItems[key];
            if (itemPickup == null || itemPickup.item == null) continue;
            
            float distance = Vector3.Distance(itemPickup.transform.position, cameraPos);
            batchItems.Add(new KeyValuePair<string, float>(key, distance));
        }
        
        // Process closest items first
        batchItems.Sort((a, b) => a.Value.CompareTo(b.Value));
        
        return (itemsProcessed, batchItems);
    }
    
    // Process remaining batches over multiple frames with distance-based priority
    private IEnumerator ProcessRemainingBatches(int totalBatches, int batchSize, int updatedCount)
    {
        int currentUpdatedCount = updatedCount;
        
        // Skip the first batch as it was already processed
        for (int batch = 1; batch < totalBatches; batch++)
        {
            var (itemsProcessed, batchItems) = ProcessWorldItemBatch(batch, batchSize, ref currentUpdatedCount);
            
            // Process the sorted batch
            foreach (var item in batchItems)
            {
                string key = item.Key;
                var itemPickup = activeWorldItems[key];
                
                // Skip null checks since we already did them
                var idComponent = itemPickup.GetComponent<ItemUniqueId>();
                bool hasMoved = itemPickup.HasMoved();
                
                // Only update position data if this item was moved or is player-dropped
                if (hasMoved || (idComponent != null && idComponent.WasPlayerDropped) || (idComponent != null && idComponent.WasStabilized))
                {
                    // Update the position data for this item
                    UpdateWorldItemPosition(key, itemPickup, false);
                    currentUpdatedCount++;
                }
                
                itemsProcessed++;
                
                // If we're processing items far from the player, we can use larger time gaps
                if (item.Value > 50f) // 50 units away
                {
                    if (itemsProcessed % 10 == 0) // Process in smaller sub-batches
                    {
                        yield return new WaitForSeconds(0.1f);
                    }
                }
            }
            
            // Adjust yield timing based on how many items were processed
            if (itemsProcessed > 50)
            {
                yield return new WaitForSeconds(0.1f); // Longer wait for large batches
            }
            else
            {
                yield return null; // Just yield the frame for small batches
            }
        }
        
        // After all batches, handle cleanup
        CleanupWorldItems();
        
        if (currentUpdatedCount > 0)
        {
            Debug.Log($"[PlayerProgression] Batch processing complete: Updated positions for {currentUpdatedCount} moved items");
        }
        
        // Save after batches complete
        SaveProgressionDataOptimized();
    }
    
    private void CleanupWorldItems()
    {
        // Clear our temporary lists first
        tempItemsToRemove.Clear();
        
        // Find items to remove
        foreach (var item in progressionData.worldItems)
        {
            if (progressionData.pickedUpItemIds.Contains(item.id) || !activeWorldItems.ContainsKey(item.id))
            {
                tempItemsToRemove.Add(item);
            }
        }
        
        // Remove items that are no longer valid
        foreach (var item in tempItemsToRemove)
        {
            progressionData.worldItems.Remove(item);
            // Return the item to the pool instead of letting it be garbage collected
            ReleaseWorldItemData(item);
        }
    }

    // Attach an item to a platform passenger zone's anchor if its position lies within a zone.
    private void TryAttachItemToPassengerZone(InvItemPickup itemPickup, Vector3 worldPosition, Quaternion worldRotation)
    {
        if (itemPickup == null) return;
        // Quickly probe triggers at the saved position
        const float probeRadius = 0.25f;
        Collider[] hits = Physics.OverlapSphere(worldPosition, probeRadius, ~0, QueryTriggerInteraction.Collide);
        if (hits == null || hits.Length == 0) return;

        for (int i = 0; i < hits.Length; i++)
        {
            var col = hits[i];
            if (col == null) continue;
            var zone = col.GetComponentInParent<KinematicPlatformPassengerZone>();
            if (zone == null) continue;
            var platform = zone.GetComponentInParent<KinematicPlatform>();
            if (platform == null) continue;

            // Parent to platform attachment anchor to prevent relative drift
            Transform parentRef = platform.AttachmentAnchor != null ? platform.AttachmentAnchor : platform.transform;
            var rb = itemPickup.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Temporarily set kinematic and freeze while anchoring
                bool wasKinematic = rb.isKinematic;
                var originalConstraints = rb.constraints;
                rb.isKinematic = true;
                rb.constraints = RigidbodyConstraints.FreezeAll;

                // Compute local pose
                Vector3 localPos = parentRef.InverseTransformPoint(worldPosition);
                Quaternion localRot = Quaternion.Inverse(parentRef.rotation) * worldRotation;

                rb.transform.SetParent(parentRef);
                rb.transform.localPosition = localPos;
                rb.transform.localRotation = localRot;

                // Keep frozen until platform resumes (PersistenceManager already resumes later)
                // We do not restore isKinematic/constraints here to avoid immediate drift during restore window
            }
            else
            {
                // Non-RB fallback
                Vector3 localPos = parentRef.InverseTransformPoint(worldPosition);
                Quaternion localRot = Quaternion.Inverse(parentRef.rotation) * worldRotation;
                itemPickup.transform.SetParent(parentRef);
                itemPickup.transform.localPosition = localPos;
                itemPickup.transform.localRotation = localRot;
            }

            // Only attach to first valid zone
            return;
        }
    }
    
    // Get a WorldItemData from the pool or create a new one
    private WorldItemData GetWorldItemData()
    {
        if (worldItemDataPool.Count > 0)
        {
            return worldItemDataPool.Dequeue();
        }
        
        return new WorldItemData();
    }

    // Return a WorldItemData to the pool
    private void ReleaseWorldItemData(WorldItemData item)
    {
        if (item == null) return;
        
        // Clear references
        item.id = null;
        item.itemName = null;
        item.containerData = "{}";
        item.position = Vector3.zero;
        item.rotation = Quaternion.identity;
        item.quantity = 0;
        item.wasDropped = false;
        item.wasStabilized = false;
        
        worldItemDataPool.Enqueue(item);
    }

    // Update an item's position and rotation data in the save file
    private void UpdateWorldItemPosition(string itemId, InvItemPickup itemPickup, bool enableLogging = true)
    {
        if (progressionData == null || string.IsNullOrEmpty(itemId) || itemPickup == null) return;
        
        // Find existing entry or create a new one
        WorldItemData itemData = progressionData.worldItems.FirstOrDefault(item => item.id == itemId);
        bool isNewItem = (itemData == null);
        
        // Check if this item is stabilized
        var idComponent = itemPickup.GetComponent<ItemUniqueId>();
        bool isStabilized = idComponent != null && idComponent.WasStabilized;
        bool isPlayerDropped = idComponent != null && idComponent.WasPlayerDropped;
        
        if (isNewItem)
        {
            // Get an item from the pool instead of creating a new one
            itemData = GetWorldItemData();
            itemData.id = itemId;
            itemData.itemName = itemPickup.item.itemName;
            itemData.quantity = itemPickup.quantity;
            itemData.wasDropped = isPlayerDropped;
            itemData.wasStabilized = isStabilized;
            progressionData.worldItems.Add(itemData);
        }
        else
        {
            // Update the stabilized and player-dropped flags
            itemData.wasDropped = isPlayerDropped;
            itemData.wasStabilized = isStabilized;
        }
        
        // Always update position and rotation
        itemData.position = itemPickup.transform.position;
        itemData.rotation = itemPickup.transform.rotation;
        
        if (enableLogging && verboseLogging)
        {
            Log($"[PlayerProgression] Updated world item position for {itemData.itemName} at {itemData.position}");
        }
        
        // Update container data for bags
        if (itemPickup.item is Bag)
        {
            var droppedStorage = itemPickup.GetComponent<InvDroppedStorageEquipment>();
            if (droppedStorage != null)
            {
                itemData.containerData = droppedStorage.GetContainerSnapshot();
            }
        }

        // Update armor energy data for armor pieces
        if (itemPickup.item is Armor armor)
        {
            itemData.armorStoredEnergy = armor.StoredEnergyContribution;
            if (enableLogging && verboseLogging && armor.StoredEnergyContribution > 0f)
            {
                Log($"[PlayerProgression] Saved armor energy for {armor.itemName}: {armor.StoredEnergyContribution}");
            }
        }
    }
    
    // Called when an item is picked up from the world - now uses pickup log instead of full save
    public void OnItemPickedUp(InvItemPickup itemPickup)
    {
        if (itemPickup == null || progressionData == null) return;
        
        // Keep a copy of needed data to avoid referencing destroyed objects later
        string itemId = GetItemId(itemPickup);
        string itemName = itemPickup.item?.itemName ?? "Unknown";
        int quantity = itemPickup.quantity;
        
        // Unregister from falling detection immediately
        if (WorldItemManager.Instance != null)
        {
            WorldItemManager.Instance.UnregisterItemFromTracking(itemPickup);
        }
        
        // Queue the operation without doing the work immediately, but with safer references
        deferredPickupOperations.Add(() => {
            // Only record if this was an actual tracked item in the world
            if (activeWorldItems.ContainsKey(itemId))
            {
                // Add to the picked up list in memory - this prevents it from being respawned
                if (!progressionData.pickedUpItemIds.Contains(itemId))
                {
                    progressionData.pickedUpItemIds.Add(itemId);
                    activeWorldItems.Remove(itemId);
                    
                    // Don't reference itemPickup here as it might be destroyed
                    // Just use the data we already captured
                    
                    // Add to pickup log instead of doing a full save
                    AddPickupLogEntry(itemId, itemName, quantity);
                }
            }
            else if (verboseLogging)
            {
                Log($"[PlayerProgression] Item pickup not registered with world items: {itemId}");
            }
        });
        
        // Let the game continue immediately, process the operation later
    }
    
    // Called when a pickup is attempted but fails (inventory full, etc.)
    public void CancelItemPickup(InvItemPickup itemPickup)
    {
        if (itemPickup == null) return;
        
        string id = GetItemId(itemPickup);
        
        // Remove from picked up list if it's there
        if (progressionData.pickedUpItemIds.Contains(id))
        {
            progressionData.pickedUpItemIds.Remove(id);
            if (verboseLogging) {
                Log($"[PlayerProgression] Item pickup canceled: {id}");
            }
        }
        
        // Add back to active tracking
        RegisterWorldItem(itemPickup);
        
        // Add a cancel entry to the pickup log
        if (itemPickup.item != null)
        {
            // We'll use a "drop" entry to cancel the pickup
            AddPickupLogEntry(id, itemPickup.item.itemName, itemPickup.quantity, false);
        }
    }
    
    // Called when a new item is dropped by the player
    public void OnItemDropped(InvItemPickup itemPickup, bool isPlayerDropped = true)
    {
        if (itemPickup == null || itemPickup.item == null) return;
        
        // Store original position and rotation to maintain exact position
        Vector3 originalPosition = itemPickup.transform.position;
        Quaternion originalRotation = itemPickup.transform.rotation;
        
        // Get existing unique ID component or create a new one if needed
        var idComponent = itemPickup.GetComponent<ItemUniqueId>();
        
        // Check if this was previously a level-placed item that was picked up
        string existingId = null;
        if (idComponent != null && !string.IsNullOrEmpty(idComponent.UniqueId))
        {
            existingId = idComponent.UniqueId;
            
            // Remove from picked up list if it's there - we're placing it back in the world
            if (progressionData.pickedUpItemIds.Contains(existingId))
            {
                progressionData.pickedUpItemIds.Remove(existingId);
                
                // Log this as a drop event
                AddPickupLogEntry(existingId, itemPickup.item.itemName, itemPickup.quantity, false);
                
                if (verboseLogging) {
                    Log($"[PlayerProgression] Level item repositioned: {existingId}");
                }
            }
        }
        
        // If this is a new drop or there was no existing ID, generate a new ID
        if (idComponent == null)
        {
            idComponent = itemPickup.gameObject.AddComponent<ItemUniqueId>();
            idComponent.UniqueId = System.Guid.NewGuid().ToString();
        }
        
        // Mark as player dropped and stabilized
        idComponent.WasPlayerDropped = isPlayerDropped;
        idComponent.WasStabilized = true; // Always stabilize dropped items
        idComponent.InitialPosition = originalPosition; // Store initial position
        
        // Register for tracking - will also save position immediately
        RegisterWorldItem(itemPickup);
        
        // Ensure position wasn't changed
        itemPickup.transform.position = originalPosition;
        itemPickup.transform.rotation = originalRotation;
        
        // For drops, we need a full save since we're adding a completely new item to the world
        // This requires saving the whole item data structure including position
        // Trigger a world items save (position data) and a pickup log entry
        SaveWorldItems();
        
        // Also add to pickup log as a drop event (so we have a complete history)
        AddPickupLogEntry(idComponent.UniqueId, itemPickup.item.itemName, itemPickup.quantity, false);
        
        // Debug log to verify the item structure for dropped items
        if (verboseLogging)
        {
            int childCount = itemPickup.transform.childCount;
            bool hasModelSwapper = itemPickup.GetComponent("InvItemModelSwapper") != null;
            Log($"[PlayerProgression] Dropped item: {itemPickup.item.itemName} - Children: {childCount}, Has ModelSwapper: {hasModelSwapper}");
        }
        
        if (verboseLogging) {
            Log($"[PlayerProgression] Item dropped and saved: {idComponent.UniqueId} at {itemPickup.transform.position}");
        }
    }
    
    // Restore world items from saved data
    private void RestoreWorldItems()
    {
        if (progressionData == null || progressionData.worldItems == null) return;
        
        // Clean up duplicate entries in world items
        CleanupDuplicateWorldItems();
        
        // Pause all platforms during item restoration to prevent movement during spawn
        PausePlatformsForItemRestore();
        
        // Log existing items for debugging (only during development)
        int activeCount = activeWorldItems.Count;
        if (activeCount > 0 && verboseLogging && Debug.isDebugBuild)
        {
            Log($"[PlayerProgression] Before restoration: {activeCount} active items in scene");
        }
        
        // Get the item dropping system for proper spawning
        var itemDropping = FindObjectOfType<InvItemDropping>();
        if (itemDropping == null)
        {
            LogError("[PlayerProgression] InvItemDropping component not found! Unable to restore world items.");
            return;
        }
        
        int restoredCount = 0;
        int skippedCount = 0;
        int updatedCount = 0;
        
        // Create a working copy to avoid modification during enumeration
        List<WorldItemData> worldItemsCopy = new List<WorldItemData>(progressionData.worldItems);
        
        // Handle each saved world item
        foreach (var worldItemData in worldItemsCopy)
        {
            // Skip if already picked up
            if (progressionData.pickedUpItemIds.Contains(worldItemData.id))
            {
                skippedCount++;
                continue;
            }
            
            // Skip if item ID is already in the active tracking dictionary
            if (activeWorldItems.ContainsKey(worldItemData.id))
            {
                // Item exists in the scene already - update its position/rotation to match saved data
                var existingItem = activeWorldItems[worldItemData.id];
                if (existingItem != null)
                {
                    // Get the current position to see if it changed
                    Vector3 oldPosition = existingItem.transform.position;
                    
                    // Set position and rotation to match saved data
                    existingItem.transform.position = worldItemData.position;
                    existingItem.transform.rotation = worldItemData.rotation;

                    // If this item belongs on a moving platform zone, attach to its anchor immediately
                    TryAttachItemToPassengerZone(existingItem, worldItemData.position, worldItemData.rotation);
                    
                    // Only log if position actually changed significantly and we're in debug mode
                    if (Vector3.Distance(oldPosition, worldItemData.position) > 0.05f && verboseLogging && Debug.isDebugBuild)
                    {
                        Log($"[PlayerProgression] Updated existing item position: {worldItemData.id} from {oldPosition} to {worldItemData.position}");
                    }
                    
                    // Mark this item as stabilized so it keeps its ID even when moved
                    var existingIdComponent = existingItem.GetComponent<ItemUniqueId>();
                    if (existingIdComponent != null)
                    {
                        existingIdComponent.WasStabilized = true;
                        existingIdComponent.InitialPosition = worldItemData.position;
                    }
                    
                    updatedCount++;
                    continue;
                }
            }
            
            // Check for duplicate items - we don't want to spawn multiple items at the same location
            bool isDuplicate = false;
            foreach (var activeItem in activeWorldItems.Values)
            {
                if (activeItem != null && 
                    Vector3.Distance(activeItem.transform.position, worldItemData.position) < 0.5f &&
                    activeItem.item.itemName == worldItemData.itemName)
                {
                    isDuplicate = true;
                    break;
                }
            }
            
            if (isDuplicate)
            {
                continue;
            }
            
            // Get the item from the database
            Item item = ItemDatabase.GetItemByName(worldItemData.itemName);
            if (item == null)
            {
                LogWarning($"[PlayerProgression] Item not found in database: {worldItemData.itemName}");
                continue;
            }
            
            // Use the clean spawning method from InvItemDropping
            GameObject itemObject = SpawnRestoredItem(item, worldItemData.position, worldItemData.rotation, itemDropping);
            
            if (itemObject == null)
            {
                LogError($"[PlayerProgression] Failed to spawn restored item: {worldItemData.itemName}");
                continue;
            }
            
            itemObject.name = $"Restored_{worldItemData.itemName}_{worldItemData.id.Substring(0, Mathf.Min(8, worldItemData.id.Length))}";
            
            // If we have a container for organization, parent to it
            if (worldItemsContainer != null)
            {
                itemObject.transform.SetParent(worldItemsContainer);
            }
            
            // Get the item pickup component (should already be set up by SpawnRestoredItem)
            var itemPickup = itemObject.GetComponent<InvItemPickup>();
            if (itemPickup == null)
            {
                LogError($"[PlayerProgression] InvItemPickup component missing on restored item: {worldItemData.itemName}");
                Destroy(itemObject);
                continue;
            }
            
            // The item should already be configured by SpawnRestoredItem, but ensure quantity is correct
            itemPickup.SetItem(item, worldItemData.quantity);
            
            // Debug log to verify clean spawning (only in verbose mode)
            if (verboseLogging)
            {
                int childCount = itemObject.transform.childCount;
                bool hasModelSwapper = itemObject.GetComponent("InvItemModelSwapper") != null;
                Log($"[PlayerProgression] Clean restore: {worldItemData.itemName} - Children: {childCount}, Has ModelSwapper: {hasModelSwapper}");
            }
            
            // Add unique ID
            var idComponent = itemObject.GetComponent<ItemUniqueId>();
            if (idComponent == null)
            {
                idComponent = itemObject.AddComponent<ItemUniqueId>();
            }
            idComponent.UniqueId = worldItemData.id;
            idComponent.WasPlayerDropped = worldItemData.wasDropped;
            
            // If this is a player-dropped item, make sure it's stabilized
            if (worldItemData.wasDropped || worldItemData.wasStabilized)
            {
                idComponent.WasStabilized = true;
                idComponent.InitialPosition = worldItemData.position;
            }
            
            // Handle container data for bags
            if (item is Bag && !string.IsNullOrEmpty(worldItemData.containerData) && worldItemData.containerData != "{}")
            {
                var droppedStorage = itemObject.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = itemObject.AddComponent<InvDroppedStorageEquipment>();
                }
                droppedStorage.SetContainerSnapshot(worldItemData.containerData);
            }

            // Handle armor energy data for armor pieces
            if (item is Armor armor && worldItemData.armorStoredEnergy > 0f)
            {
                armor.StoredEnergyContribution = worldItemData.armorStoredEnergy;
                if (verboseLogging)
                {
                    Log($"[PlayerProgression] Restored armor energy for {armor.itemName}: {worldItemData.armorStoredEnergy}");
                }
            }
            
            // Register for tracking
            RegisterWorldItem(itemPickup);
            
            // Ensure the position is exactly as specified after all operations
            itemObject.transform.position = worldItemData.position;
            itemObject.transform.rotation = worldItemData.rotation;

            // If this item belongs on a moving platform zone, attach to its anchor immediately
            TryAttachItemToPassengerZone(itemPickup, worldItemData.position, worldItemData.rotation);
            
            // Restore velocity if it was saved (and not too old)
            if (worldItemData.velocityTimestamp > 0 && 
                Time.time - worldItemData.velocityTimestamp < 30f && // Don't restore very old velocities
                (worldItemData.velocity.magnitude > 0.1f || worldItemData.angularVelocity.magnitude > 0.1f))
            {
                var rigidbody = itemObject.GetComponent<Rigidbody>();
                if (rigidbody != null)
                {
                    rigidbody.linearVelocity = worldItemData.velocity;
                    rigidbody.angularVelocity = worldItemData.angularVelocity;
                    rigidbody.isKinematic = worldItemData.isKinematic;
                    
                    if (verboseLogging) {
                        Log($"[PlayerProgression] Restored velocity for {worldItemData.itemName}: {worldItemData.velocity}");
                    }
                }
            }
            
            restoredCount++;
        }
        
        if ((restoredCount > 0 || updatedCount > 0) && verboseLogging)
        {
            Log($"[PlayerProgression] Restored {restoredCount} new world items, updated {updatedCount} existing items, skipped {skippedCount} picked up items");
        }
        
        // Resume platforms after item restoration is complete
        ResumePlatformsAfterItemRestore();
    }
    
    // Clean up duplicate entries in the world items list
    private void CleanupDuplicateWorldItems()
    {
        if (progressionData == null || progressionData.worldItems == null) return;
        
        // First group by position to find duplicates
        var itemsByPosition = new Dictionary<string, List<WorldItemData>>();
        
        foreach (var item in progressionData.worldItems)
        {
            // Round positions to reduce floating point issues
            string posKey = $"{Mathf.Round(item.position.x * 10) / 10}_{Mathf.Round(item.position.y * 10) / 10}_{Mathf.Round(item.position.z * 10) / 10}";
            
            if (!itemsByPosition.ContainsKey(posKey))
            {
                itemsByPosition[posKey] = new List<WorldItemData>();
            }
            
            itemsByPosition[posKey].Add(item);
        }
        
        // Track items to remove (can't modify during enumeration)
        List<WorldItemData> itemsToRemove = new List<WorldItemData>();
        
        // For each position with multiple items, keep only one (preferably player dropped)
        foreach (var posGroup in itemsByPosition.Values)
        {
            if (posGroup.Count > 1)
            {
                if (verboseLogging) {
                    Log($"[PlayerProgression] Found {posGroup.Count} duplicate items at position {posGroup[0].position}");
                }
                
                // Sort to prioritize keeping player-dropped items
                posGroup.Sort((a, b) => b.wasDropped.CompareTo(a.wasDropped));
                
                // Keep the first one, mark others for removal
                for (int i = 1; i < posGroup.Count; i++)
                {
                    itemsToRemove.Add(posGroup[i]);
                }
            }
        }
        
        // Remove all duplicates
        foreach (var item in itemsToRemove)
        {
            progressionData.worldItems.Remove(item);
            if (verboseLogging) {
                Log($"[PlayerProgression] Removed duplicate item: {item.id} at {item.position}");
            }
        }
        
        if (itemsToRemove.Count > 0) {
            Log($"[PlayerProgression] Cleaned up {itemsToRemove.Count} duplicate items");
        }
        
        // Save the cleaned up data
        if (itemsToRemove.Count > 0)
        {
            SaveProgressionData();
        }
    }
    
    #endregion

    // Helper method to serialize inventory data
    private string SerializeInventory(InvItemContainer container)
    {
        if (container == null) return "{}";
        return SerializableItemList.FromContainer(container).ToJson();
    }
    
    // Save player inventory and equipment data
    private void SaveInventoryAndEquipment()
    {
        Debug.Log("[INVENTORY_DEBUG] SaveInventoryAndEquipment called");
        if (progressionData == null)
        {
            Debug.LogError("[INVENTORY_DEBUG] SaveInventoryAndEquipment: progressionData is null!");
            return;
        }
        
        try 
        {
            // Always get the currency from CurrencyManager, which is the source of truth
            if (currencyManager != null) {
                progressionData.playerMoney = currencyManager.CurrentCurrency;
                if (verboseLogging) {
                    Log($"[PlayerProgression] Saved currency from CurrencyManager: {progressionData.playerMoney}");
                }
            }
            else if (playerStatus != null) {
                // Fallback to PlayerStatus in case CurrencyManager is missing
                progressionData.playerMoney = playerStatus.Currency;
                if (verboseLogging) {
                    Log($"[PlayerProgression] Saved player currency from PlayerStatus: {progressionData.playerMoney}");
                }
            }
            
            // Save player inventory
            if (playerInventory != null)
            {
                progressionData.playerInventoryData = SerializeInventory(playerInventory);
                if (verboseLogging) {
                    Log($"[PlayerProgression] Saved player inventory: {progressionData.playerInventoryData.Substring(0, Mathf.Min(50, progressionData.playerInventoryData.Length))}...");
                }
            }
            
            // Save equipment data
            Debug.Log("[INVENTORY_DEBUG] Clearing equipment data and saving new data");
            progressionData.equipmentData.Clear();

            if (equipmentManager != null)
            {
                Debug.Log($"[INVENTORY_DEBUG] Found EquipmentManager, processing {equipmentManager.GetEquipmentSlots().Count()} slots");
                foreach (var slot in equipmentManager.GetEquipmentSlots())
                {
                    var slotData = new EquipmentSlotData
                    {
                        slotType = slot.slotType,
                        equippedItemName = slot.equippedItem?.itemName ?? "",
                        containerData = slot.storageContainer != null ?
                                         SerializeInventory(slot.storageContainer) :
                                         "{}",
                        armorStoredEnergy = slot.equippedItem is Armor armor ? armor.StoredEnergyContribution : 0f
                    };

                    progressionData.equipmentData.Add(slotData);
                    Debug.Log($"[INVENTORY_DEBUG] Saved equipment slot {slot.slotType}: '{slotData.equippedItemName}' with container data: {slotData.containerData.Substring(0, Math.Min(100, slotData.containerData.Length))}...");
                    if (verboseLogging) {
                        Log($"[PlayerProgression] Saved equipment slot {slot.slotType}: {slotData.equippedItemName} (armor energy: {slotData.armorStoredEnergy})");
                    }
                }
                Debug.Log($"[INVENTORY_DEBUG] Total equipment slots saved: {progressionData.equipmentData.Count}");
            }
            else
            {
                Debug.LogError("[INVENTORY_DEBUG] EquipmentManager is null! Cannot save equipment data!");
            }
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Error saving inventory and equipment: {e.Message}");
        }
    }
    
    // Restore player inventory and equipment from saved data
    private void RestoreInventoryAndEquipment()
    {
        Debug.Log("[INVENTORY_DEBUG] RestoreInventoryAndEquipment called");
        if (progressionData == null)
        {
            Debug.LogError("[INVENTORY_DEBUG] RestoreInventoryAndEquipment: progressionData is null!");
            return;
        }

        Debug.Log($"[INVENTORY_DEBUG] Found {progressionData.equipmentData.Count} equipment slots in save data");

        // Make sure references are valid - attempt to find them if null
        if (equipmentManager == null) {
            Debug.Log("[INVENTORY_DEBUG] EquipmentManager is null, searching for it...");
            equipmentManager = FindObjectOfType<EquipmentManager>();
            if (equipmentManager == null) {
                Debug.LogError("[INVENTORY_DEBUG] Failed to find EquipmentManager during restore!");
                LogError("[PlayerProgression] Failed to find EquipmentManager during restore!");
                // We'll continue with other parts of restoration
            }
            else
            {
                Debug.Log("[INVENTORY_DEBUG] Found EquipmentManager successfully");
            }
        }
        else
        {
            Debug.Log("[INVENTORY_DEBUG] EquipmentManager reference already exists");
        }
        
        // Restore player inventory
        if (playerInventory != null && !string.IsNullOrEmpty(progressionData.playerInventoryData) 
            && progressionData.playerInventoryData != "{}")
        {
            try
            {
                var itemList = SerializableItemList.FromJson(progressionData.playerInventoryData);
                RestoreItemsToContainer(playerInventory, itemList);
                if (verboseLogging) {
                    Log("[PlayerProgression] Restored player inventory");
                }
            }
            catch (Exception e)
            {
                LogError($"[PlayerProgression] Failed to restore player inventory: {e.Message}");
            }
        }
        
        // Restore equipment only if equipmentManager is available
        if (equipmentManager != null && progressionData.equipmentData != null && progressionData.equipmentData.Count > 0)
        {
            Debug.Log($"[INVENTORY_DEBUG] Starting equipment restoration for {progressionData.equipmentData.Count} slots");
            foreach (var slotData in progressionData.equipmentData)
            {
                Debug.Log($"[INVENTORY_DEBUG] Processing slot {slotData.slotType} with item '{slotData.equippedItemName}'");
                try
                {
                    // Skip if no item was equipped
                    if (string.IsNullOrEmpty(slotData.equippedItemName))
                    {
                        Debug.Log($"[INVENTORY_DEBUG] Skipping empty slot {slotData.slotType}");
                        continue;
                    }

                    // Get the item from the item database
                    Debug.Log($"[INVENTORY_DEBUG] Looking up item '{slotData.equippedItemName}' in database");
                    var item = ItemDatabase.GetItemByName(slotData.equippedItemName);
                    if (item == null)
                    {
                        Debug.LogError($"[INVENTORY_DEBUG] Item not found in database: {slotData.equippedItemName}");
                        LogWarning($"[PlayerProgression] Item not found in database: {slotData.equippedItemName}");
                        continue;
                    }
                    Debug.Log($"[INVENTORY_DEBUG] Found item '{slotData.equippedItemName}' in database");
                    
                    // Equip the item if it's an equipment
                    if (item is EquipmentBase equipment)
                    {
                        equipmentManager.EquipItem(equipment);
                        if (verboseLogging) {
                            Log($"[PlayerProgression] Equipped item: {equipment.itemName}");
                        }

                        // Restore armor energy if this is armor
                        if (equipment is Armor armor && slotData.armorStoredEnergy > 0f)
                        {
                            armor.StoredEnergyContribution = slotData.armorStoredEnergy;
                            if (verboseLogging) {
                                Log($"[PlayerProgression] Restored armor energy: {armor.itemName} = {slotData.armorStoredEnergy}");
                            }
                        }

                        // Restore container data if any
                        if (!string.IsNullOrEmpty(slotData.containerData) && slotData.containerData != "{}")
                        {
                            var itemList = SerializableItemList.FromJson(slotData.containerData);
                            var slot = equipmentManager.GetEquipmentSlot(slotData.slotType);

                            if (slot != null && slot.storageContainer != null)
                            {
                                RestoreItemsToContainer(slot.storageContainer, itemList);
                                if (verboseLogging) {
                                    Log($"[PlayerProgression] Restored items to equipment container: {slotData.slotType}");
                                }
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    LogError($"[PlayerProgression] Failed to restore equipment slot {slotData.slotType}: {e.Message}");
                }
            }

            Debug.Log("[INVENTORY_DEBUG] Equipment restoration completed, updating weight and UI");
            // Update weight after restoring all items
            equipmentManager.UpdateWeightAfterItemChange();

            // Only call ForceImmediateUIUpdate if equipmentManager exists
            equipmentManager.ForceImmediateUIUpdate();
            Debug.Log("[INVENTORY_DEBUG] Equipment restoration fully complete");
        }
        else if (equipmentManager == null) {
            Debug.LogError("[INVENTORY_DEBUG] Skipping equipment restoration - EquipmentManager is null");
            LogWarning("[PlayerProgression] Skipping equipment restoration - EquipmentManager is null");
        }
        else if (progressionData.equipmentData == null || progressionData.equipmentData.Count == 0) {
            Debug.LogWarning("[INVENTORY_DEBUG] No equipment data found in save file - starting with empty inventory");
        }
    }
    
    // Helper to restore items to a container from serialized data
    private void RestoreItemsToContainer(InvItemContainer container, SerializableItemList itemList)
    {
        if (container == null || itemList == null || itemList.items == null) return;
        
        // Clear the container first
        container.Clear();
        
        // Add each item to the container
        foreach (var serialItem in itemList.items)
        {
            var item = ItemDatabase.GetItemByName(serialItem.itemName);
            if (item != null)
            {
                container.AddItemToSlot(
                    item, 
                    serialItem.quantity, 
                    serialItem.gridPosition, 
                    serialItem.isRotated
                );
            }
            else
            {
                Debug.LogWarning($"[PlayerProgression] Item not found in database: {serialItem.itemName}");
            }
        }
    }

    public void SaveProgressionData()
    {
        if (progressionData == null) return;
        
        try
        {
            // Update the version info before saving
            progressionData.gameVersion = Application.version;
            
            // Make sure we have the latest currency from PlayerStatus
            if (playerStatus != null)
            {
                progressionData.playerMoney = playerStatus.Currency;
            }
            
            // Serialize to JSON
            string jsonData = JsonUtility.ToJson(progressionData);
            
            // Encrypt the data
            string encryptedData = EncryptString(jsonData);
            if (string.IsNullOrEmpty(encryptedData))
            {
                throw new Exception("Encryption returned empty data");
            }
            
            // Use a safer file writing approach
            string saveFilePath = SaveFilePath;
            string tempFilePath = saveFilePath + ".tmp";
            string backupFilePath = saveFilePath + ".bak";
            
            // First write to a temporary file
            File.WriteAllText(tempFilePath, encryptedData);
            
            // Verify the temp file was written correctly
            if (!File.Exists(tempFilePath))
            {
                throw new Exception("Failed to write temporary save file");
            }
            
            // Try to read back the temp file to verify it's valid
            string tempFileContent = File.ReadAllText(tempFilePath);
            if (string.IsNullOrEmpty(tempFileContent))
            {
                throw new Exception("Temporary save file is empty");
            }
            
            // If the temp file was written successfully, keep a backup of the old file if it exists
            if (File.Exists(saveFilePath))
            {
                try
                {
                    // Keep a backup of the previous save
                    if (File.Exists(backupFilePath))
                        File.Delete(backupFilePath);
                        
                    File.Copy(saveFilePath, backupFilePath);
                }
                catch (Exception backupEx)
                {
                    LogWarning($"[PlayerProgression] Failed to create backup: {backupEx.Message}");
                    // Continue anyway - having a new save is better than nothing
                }
            }
            
            // Now move the temp file to the real location
            if (File.Exists(saveFilePath))
                File.Delete(saveFilePath);
                
            File.Move(tempFilePath, saveFilePath);
            
            // Verify the final save file exists and has content
            if (!File.Exists(saveFilePath))
            {
                throw new Exception("Final save file does not exist after move operation");
            }
            
            string finalFileContent = File.ReadAllText(saveFilePath);
            if (string.IsNullOrEmpty(finalFileContent))
            {
                throw new Exception("Final save file is empty after move operation");
            }
            
            // Write to PlayerPrefs as a secondary backup
            PlayerPrefs.SetString(PROGRESSION_SAVE_KEY, encryptedData);
            
            // Also save the direct currency value as a last fallback
            if (playerStatus != null)
            {
                PlayerPrefs.SetInt("player_currency_last", playerStatus.Currency);
            }
            else if (progressionData != null)
            {
                PlayerPrefs.SetInt("player_currency_last", progressionData.playerMoney);
            }
            
            PlayerPrefs.Save();
            
            Log("[PlayerProgression] Save completed successfully");
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Failed to save progression data: {e.Message}");
            HandleCorruptedSave(SaveFilePath, e);
        }
    }

    // Modified version of SaveProgressionData that's optimized for large scenes
    public void SaveProgressionDataOptimized()
    {
        if (progressionData == null) return;
        
        try
        {
            // Get the latest currency from PlayerStatus if available
            if (playerStatus != null) {
                progressionData.playerMoney = playerStatus.Currency;
                
                // Also update energy values
                progressionData.playerCurrentEnergy = playerStatus.currentEnergy;
            }
            
            // Update the version info before saving
            progressionData.gameVersion = Application.version;
            
            // Capture these values on the main thread before passing to background thread
            string saveFilePath = SaveFilePath;
            string tempFilePath = saveFilePath + ".tmp";
            string backupFilePath = saveFilePath + ".bak";
            string saveKeyForThread = PROGRESSION_SAVE_KEY;
            
            // First convert to JSON 
            string jsonData = JsonUtility.ToJson(progressionData);
            
            // Then encrypt
            string encryptedData = EncryptString(jsonData);
            
            // First write to a temporary file
            File.WriteAllText(tempFilePath, encryptedData);
            
            // If the temp file was written successfully, keep a backup of the old file if it exists
            if (File.Exists(saveFilePath))
            {
                try
                {
                    // Keep a backup of the previous save
                    if (File.Exists(backupFilePath))
                        File.Delete(backupFilePath);
                        
                    File.Copy(saveFilePath, backupFilePath);
                }
                catch (Exception backupEx)
                {
                    LogWarning($"[PlayerProgression] Failed to create backup: {backupEx.Message}");
                    // Continue anyway - having a new save is better than nothing
                }
            }
            
            // Now move the temp file to the real location
            if (File.Exists(saveFilePath))
                File.Delete(saveFilePath);
                
            File.Move(tempFilePath, saveFilePath);
            
            // Also save to PlayerPrefs as backup
            PlayerPrefs.SetString(saveKeyForThread, encryptedData);
            
            // Additionally, save a direct currency backup
            if (playerStatus != null)
            {
                PlayerPrefs.SetInt("player_currency_last", playerStatus.Currency);
            }
            else if (progressionData != null)
            {
                PlayerPrefs.SetInt("player_currency_last", progressionData.playerMoney);
            }
            
            PlayerPrefs.Save();
            
            if (verboseLogging) {
                Log("[PlayerProgression] Save completed successfully");
            }
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Failed to save progression data: {e.Message}");
            
            // Try to handle corrupted save
            HandleCorruptedSave(SaveFilePath, e);
        }
    }

    private void LoadProgressionData()
    {
        try
        {
            bool loadedSuccessfully = false;
            Exception lastError = null;
            string filePath = SaveFilePath;
            
            // First try to load from main file
            if (File.Exists(filePath))
            {
                try
                {
                    string encryptedData = File.ReadAllText(filePath);
                    string decryptedJson = DecryptString(encryptedData);
                    
                    if (!string.IsNullOrEmpty(decryptedJson))
                    {
                        ProgressionData loadedData = JsonUtility.FromJson<ProgressionData>(decryptedJson);
                        
                        if (IsValidProgressionData(loadedData))
                        {
                            // Fix: Create a reference progression data to compare against
                            var referenceData = new ProgressionData();
                            referenceData.saveFormatVersion = 1; // Current version
                            
                            // Check if this is an older version that needs migration
                            if (loadedData.saveFormatVersion < referenceData.saveFormatVersion)
                            {
                                Log($"[PlayerProgression] Migrating save from version {loadedData.saveFormatVersion} to {referenceData.saveFormatVersion}");
                                MigrateToVersion1(loadedData);
                            }
                            
                            progressionData = loadedData;
                            loadedSuccessfully = true;
                            Log($"[PlayerProgression] Successfully loaded save. Stored currency: {progressionData.playerMoney}");
                        }
                        else
                        {
                            // Instead of failing immediately, try to repair the data
                            LogWarning("[PlayerProgression] Save data validation failed, attempting to repair...");
                            if (AttemptDataRepair(loadedData))
                            {
                                progressionData = loadedData;
                                loadedSuccessfully = true;
                                Log("[PlayerProgression] Successfully repaired and loaded save data");
                            }
                            else
                            {
                                LogError("[PlayerProgression] Could not repair save data, will try backup");
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    lastError = e;
                    LogError($"[PlayerProgression] Error loading save file: {e.Message}");
                }
                
                // If we failed to load from file, try the PlayerPrefs backup
                if (!loadedSuccessfully)
                {
                    try
                    {
                        string playerPrefsData = PlayerPrefs.GetString(PROGRESSION_SAVE_KEY, "");
                        if (!string.IsNullOrEmpty(playerPrefsData))
                        {
                            string decryptedJson = DecryptString(playerPrefsData);
                            if (!string.IsNullOrEmpty(decryptedJson))
                            {
                                ProgressionData loadedData = JsonUtility.FromJson<ProgressionData>(decryptedJson);
                                if (IsValidProgressionData(loadedData) || AttemptDataRepair(loadedData))
                                {
                                    progressionData = loadedData;
                                    loadedSuccessfully = true;
                                    Log("[PlayerProgression] Successfully loaded from PlayerPrefs backup");
                                    
                                    // Handle corrupted save files
                                    if (lastError != null)
                                    {
                                        HandleCorruptedSave(filePath, lastError);
                                    }
                                    return;
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        LogWarning($"[PlayerProgression] Failed to load backup file: {e.Message}");
                    }
                }
            }
            
            // If both files failed, try PlayerPrefs
            if (!loadedSuccessfully)
            {
                try
                {
                    string playerPrefsData = PlayerPrefs.GetString(PROGRESSION_SAVE_KEY, "");
                    if (!string.IsNullOrEmpty(playerPrefsData))
                    {
                        string decryptedJson = DecryptString(playerPrefsData);
                        if (!string.IsNullOrEmpty(decryptedJson))
                        {
                            ProgressionData loadedData = JsonUtility.FromJson<ProgressionData>(decryptedJson);
                            if (IsValidProgressionData(loadedData) || AttemptDataRepair(loadedData))
                            {
                                progressionData = loadedData;
                                loadedSuccessfully = true;
                                Log("[PlayerProgression] Successfully loaded from PlayerPrefs backup");
                                
                                // Handle corrupted save files
                                if (lastError != null)
                                {
                                    HandleCorruptedSave(filePath, lastError);
                                }
                                return;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    LogError($"[PlayerProgression] Failed to load from PlayerPrefs: {e.Message}");
                }
            }
            
            // If all attempts failed, create new data
            if (!loadedSuccessfully)
            {
                LogWarning("[PlayerProgression] All load attempts failed, creating new save data");
                CreateNewProgressionData();
            }
            
            // After loading, check for direct currency storage in PlayerPrefs as a last resort
            if (PlayerPrefs.HasKey("emergency_currency_backup"))
            {
                int backupCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
                Log($"[PlayerProgression] Found emergency currency backup during load: {backupCurrency}");
                
                if (backupCurrency > progressionData.playerMoney)
                {
                    Log($"[PlayerProgression] Using emergency currency backup: {backupCurrency} instead of {progressionData.playerMoney}");
                    progressionData.playerMoney = backupCurrency;
                }
            }
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Critical error loading progression data: {e.Message}");
            CreateNewProgressionData();
            
            // Try to recover currency specifically
            RecoverCurrencyFromAllSources();
        }

        // Add this code near the end of the LoadProgressionData method, just before the final catch block
        // After creating new data or loading, check all currency backup sources
        int bestCurrencyValue = progressionData?.playerMoney ?? 0;

        // Check emergency backup
        if (PlayerPrefs.HasKey("emergency_currency_backup"))
        {
            int emergencyCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
            if (verboseLogging) {
                Log($"[PlayerProgression] Found emergency currency backup during load: {emergencyCurrency}");
            }
            
            if (emergencyCurrency > bestCurrencyValue)
            {
                bestCurrencyValue = emergencyCurrency;
                Log($"[PlayerProgression] Using emergency currency backup: {bestCurrencyValue}");
            }
        }

        // Check simple direct key backup
        if (PlayerPrefs.HasKey("player_currency_last"))
        {
            int lastCurrency = PlayerPrefs.GetInt("player_currency_last", 0);
            if (verboseLogging) {
                Log($"[PlayerProgression] Found last currency value during load: {lastCurrency}");
            }
            
            if (lastCurrency > bestCurrencyValue)
            {
                bestCurrencyValue = lastCurrency;
                Log($"[PlayerProgression] Using last currency backup: {bestCurrencyValue}");
            }
        }

        // Update the progression data with the best currency value found
        if (progressionData != null && bestCurrencyValue > progressionData.playerMoney)
        {
            Log($"[PlayerProgression] Updating currency from {progressionData.playerMoney} to {bestCurrencyValue}");
            progressionData.playerMoney = bestCurrencyValue;
            SaveProgressionDataOptimized();
        }
    }

    // New method to attempt repairing corrupted save data instead of resetting
    private bool AttemptDataRepair(ProgressionData data)
    {
        if (data == null) return false;
        
        bool wasRepaired = false;
        
        // Repair missing fields with defaults
        if (string.IsNullOrEmpty(data.saveVersion))
        {
            data.saveVersion = "1.0";
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing saveVersion");
        }
        
        if (data.worldItems == null)
        {
            data.worldItems = new List<WorldItemData>();
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing worldItems list");
        }
        
        if (data.heavyObjects == null)
        {
            data.heavyObjects = new List<HeavyObjectData>();
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing heavyObjects list");
        }
        
        if (data.pickedUpItemIds == null)
        {
            data.pickedUpItemIds = new List<string>();
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing pickedUpItemIds list");
        }
        
        if (data.equipmentData == null)
        {
            data.equipmentData = new List<EquipmentSlotData>();
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing equipmentData list");
        }
        
        if (string.IsNullOrEmpty(data.playerInventoryData))
        {
            data.playerInventoryData = "{}";
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing playerInventoryData");
        }
        
        if (string.IsNullOrEmpty(data.gameVersion))
        {
            data.gameVersion = "0.1.0"; // Assume it's from an older version
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing gameVersion");
        }
        
        if (data.saveFormatVersion == 0)
        {
            data.saveFormatVersion = 1;
            wasRepaired = true;
            Log("[PlayerProgression] Repaired missing saveFormatVersion");
        }
        
        // Ensure energy values are reasonable
        if (data.playerCurrentEnergy < 0)
        {
            data.playerCurrentEnergy = 100f;
            wasRepaired = true;
            Log("[PlayerProgression] Repaired invalid playerCurrentEnergy");
        }
        
        if (wasRepaired)
        {
            Log("[PlayerProgression] Successfully repaired save data");
        }
        
        return wasRepaired;
    }

    // Special method to recover currency from any possible source
    private void RecoverCurrencyFromAllSources()
    {
        try
        {
            int bestCurrency = 0;
            
            // Try emergency backup
            if (PlayerPrefs.HasKey("emergency_currency_backup"))
            {
                int emergencyCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
                if (emergencyCurrency > bestCurrency)
                {
                    bestCurrency = emergencyCurrency;
                    Debug.Log($"[PlayerProgression] Recovered currency from emergency backup: {bestCurrency}");
                }
            }
            
            // Try direct PlayerPrefs (some older versions might have used this)
            if (PlayerPrefs.HasKey("player_currency"))
            {
                int directCurrency = PlayerPrefs.GetInt("player_currency", 0);
                if (directCurrency > bestCurrency)
                {
                    bestCurrency = directCurrency;
                    Debug.Log($"[PlayerProgression] Recovered currency from direct PlayerPrefs: {bestCurrency}");
                }
            }
            
            // If we found any currency, use it
            if (bestCurrency > 0)
            {
                progressionData.playerMoney = bestCurrency;
                Debug.Log($"[PlayerProgression] Set recovered currency to: {bestCurrency}");
                
                // Save it to ensure it's not lost
                SaveProgressionDataOptimized();
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"[PlayerProgression] Failed to recover currency: {ex.Message}");
        }
    }

    private bool IsValidProgressionData(ProgressionData data)
    {
        if (data == null) return false;
        
        // Make validation more lenient - these can be repaired
        // Basic validation checks that can't be easily repaired
        return true; // Let AttemptDataRepair handle the fixes
    }

    private void CreateNewProgressionData()
    {
        progressionData = new ProgressionData();
        progressionData.saveFormatVersion = 1;
        progressionData.gameVersion = Application.version;
        progressionData.stashRows = DEFAULT_STASH_ROWS;
        progressionData.worldItems = new List<WorldItemData>();
        progressionData.pickedUpItemIds = new List<string>();
        
        Debug.Log("[PlayerProgression] Created new progression data");
    }

    private string EncryptString(string text)
    {
        try
        {
            if (string.IsNullOrEmpty(text))
                return string.Empty;

            // Use a consistent key size
            byte[] key = new byte[32];
            byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
            Array.Copy(keyBytes, key, Math.Min(keyBytes.Length, 32));

            using (Aes aes = Aes.Create())
            {
                aes.Key = key;
                aes.GenerateIV();
                aes.Padding = PaddingMode.PKCS7;
                aes.Mode = CipherMode.CBC;

                ICryptoTransform encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                using (MemoryStream msEncrypt = new MemoryStream())
                {
                    // Write the IV length and IV first
                    byte[] ivLength = BitConverter.GetBytes(aes.IV.Length);
                    msEncrypt.Write(ivLength, 0, 4);
                    msEncrypt.Write(aes.IV, 0, aes.IV.Length);

                    using (CryptoStream csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                    {
                        byte[] textBytes = Encoding.UTF8.GetBytes(text);
                        csEncrypt.Write(textBytes, 0, textBytes.Length);
                        csEncrypt.FlushFinalBlock();
                    }

                    return Convert.ToBase64String(msEncrypt.ToArray());
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[PlayerProgression] Encryption failed: {e.Message}");
            // In case of encryption failure, save unencrypted but with a marker
            return "UNENCRYPTED:" + text;
        }
    }

    private string DecryptString(string cipherText)
    {
        try
        {
            if (string.IsNullOrEmpty(cipherText))
                return string.Empty;

            // Check if this is unencrypted data
            if (cipherText.StartsWith("UNENCRYPTED:"))
                return cipherText.Substring("UNENCRYPTED:".Length);

            // Use a consistent key size
            byte[] key = new byte[32];
            byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
            Array.Copy(keyBytes, key, Math.Min(keyBytes.Length, 32));

            byte[] fullCipher = Convert.FromBase64String(cipherText);

            using (MemoryStream msDecrypt = new MemoryStream(fullCipher))
            {
                // Read the IV length
                byte[] ivLengthBytes = new byte[4];
                if (msDecrypt.Read(ivLengthBytes, 0, 4) != 4)
                    throw new Exception("Invalid encrypted data - IV length missing");

                int ivLength = BitConverter.ToInt32(ivLengthBytes, 0);
                if (ivLength <= 0 || ivLength > 64) // Sanity check
                    throw new Exception("Invalid IV length");

                // Read the IV
                byte[] iv = new byte[ivLength];
                if (msDecrypt.Read(iv, 0, ivLength) != ivLength)
                    throw new Exception("Invalid encrypted data - IV missing");

                using (Aes aes = Aes.Create())
                {
                    aes.Key = key;
                    aes.IV = iv;
                    aes.Padding = PaddingMode.PKCS7;
                    aes.Mode = CipherMode.CBC;

                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    // Get the cipher text bytes
                    byte[] cipherBytes = new byte[msDecrypt.Length - msDecrypt.Position];
                    msDecrypt.Read(cipherBytes, 0, cipherBytes.Length);

                    using (MemoryStream plaintext = new MemoryStream())
                    {
                        using (CryptoStream csDecrypt = new CryptoStream(plaintext, decryptor, CryptoStreamMode.Write))
                        {
                            csDecrypt.Write(cipherBytes, 0, cipherBytes.Length);
                            csDecrypt.FlushFinalBlock();
                        }

                        return Encoding.UTF8.GetString(plaintext.ToArray());
                    }
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[PlayerProgression] Decryption failed: {e.Message}");
            return string.Empty;
        }
    }

    public void UpgradeStashSize()
    {
        progressionData.stashRows++;
        SaveProgressionData();
    }

    public int GetCurrentStashRows()
    {
        if (progressionData == null)
        {
            progressionData = new ProgressionData();
        }
        return progressionData.stashRows;
    }

    public int GetDefaultStashRows()
    {
        return DEFAULT_STASH_ROWS;
    }

    public void MigrateStashRows(int oldRows)
    {
        progressionData.stashRows = oldRows;
        SaveProgressionData();
        Debug.Log($"[PlayerProgression] Migrated stash rows to: {oldRows}");
    }
    
    public void AddMoney(int amount)
    {
        // Always use CurrencyManager which is the source of truth
        if (currencyManager != null) {
            currencyManager.AddCurrency(amount);
        }
        else {
            Debug.LogError("[PlayerProgression] CurrencyManager not found when trying to add money!");
            // Create a CurrencyManager if missing
            GameObject cmObj = new GameObject("CurrencyManager");
            currencyManager = cmObj.AddComponent<CurrencyManager>();
            currencyManager.AddCurrency(amount);
        }
    }
    
    // Method to manually trigger a save of inventory and equipment
    public void SaveInventoryAndEquipmentNow()
    {
        Debug.Log("[INVENTORY_DEBUG] SaveInventoryAndEquipmentNow called");
        // First ensure currency is synchronized
        if (playerStatus != null) {
            progressionData.playerMoney = playerStatus.Currency;
            Debug.Log($"[PlayerProgression] Manual save: Saving player currency: {progressionData.playerMoney}");
        }

        SaveInventoryAndEquipment();
        SaveProgressionData();
        Debug.Log("[PlayerProgression] Manually saved inventory and equipment");
        Debug.Log("[INVENTORY_DEBUG] SaveInventoryAndEquipmentNow completed");
    }
    
    /// <summary>
    /// Set the container for world items
    /// </summary>
    public void SetWorldItemsContainer(Transform container)
    {
        if (container != null)
        {
            worldItemsContainer = container;
            worldItemsContainerInitialized = true;
            Debug.Log("[PlayerProgression] World items container set");
        }
    }
    
    /// <summary>
    /// Reset all world item tracking data
    /// </summary>
    public void ResetWorldItemData()
    {
        if (progressionData != null)
        {
            progressionData.worldItems.Clear();
            progressionData.pickedUpItemIds.Clear();
            activeWorldItems.Clear();
            
            // Save the cleared data
            SaveProgressionData();
            Debug.Log("[PlayerProgression] World item data has been reset");
        }
    }

    /// <summary>
    /// Delete all saved items and start fresh - use with caution!
    /// </summary>
    public void DeleteAllWorldItems()
    {
        if (progressionData != null)
        {
            progressionData.worldItems.Clear();
            progressionData.pickedUpItemIds.Clear();
            activeWorldItems.Clear();
            
            // Destroy all existing items in the scene
            var existingItems = FindObjectsOfType<InvItemPickup>();
            foreach (var item in existingItems)
            {
                Destroy(item.gameObject);
            }
            
            // Save the cleared data
            SaveProgressionData();
            Debug.Log("[PlayerProgression] Deleted all world items - starting fresh");
        }
    }

    /// <summary>
    /// Try to recover from a corrupted save backup file
    /// </summary>
    public bool TryRecoverFromBackup()
    {
        try
        {
            string saveDirectory = Path.GetDirectoryName(SaveFilePath);
            var backupFiles = Directory.GetFiles(saveDirectory, "*.corrupted_*.bak");
            
            if (backupFiles.Length == 0)
            {
                Debug.LogWarning("[PlayerProgression] No backup files found for recovery");
                return false;
            }
            
            // Try to load the most recent backup file
            string mostRecentBackup = backupFiles.OrderByDescending(File.GetCreationTime).First();
            Debug.Log($"[PlayerProgression] Attempting to recover from backup: {mostRecentBackup}");
            
            string backupContent = File.ReadAllText(mostRecentBackup);
            string decryptedJson = DecryptString(backupContent);
            
            if (!string.IsNullOrEmpty(decryptedJson))
            {
                ProgressionData backupData = JsonUtility.FromJson<ProgressionData>(decryptedJson);
                
                // Try to repair the data if needed
                if (IsValidProgressionData(backupData) || AttemptDataRepair(backupData))
                {
                    progressionData = backupData;
                    SaveProgressionData();
                    Debug.Log($"[PlayerProgression] Successfully recovered from backup! Currency: {progressionData.playerMoney}");
                    return true;
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"[PlayerProgression] Failed to recover from backup: {e.Message}");
        }
        
        return false;
    }

    /// <summary>
    /// Clean up messy item structures by respawning them using the clean method
    /// </summary>
    public void CleanupMessyItems()
    {
        var itemDropping = FindObjectOfType<InvItemDropping>();
        if (itemDropping == null)
        {
            Debug.LogError("[PlayerProgression] InvItemDropping component not found for cleanup!");
            return;
        }
        
        // Find all items that might have messy structures
        var allItems = FindObjectsOfType<InvItemPickup>();
        int cleanedCount = 0;
        
        foreach (var item in allItems)
        {
            if (item == null || item.item == null) continue;
            
            // Check if this item has a messy structure (has children or model swapper)
            bool hasChildren = item.transform.childCount > 0;
            bool hasModelSwapper = item.GetComponent("InvItemModelSwapper") != null;
            
            if (hasChildren || hasModelSwapper)
            {
                // Store the item data
                string itemId = GetItemId(item);
                Vector3 position = item.transform.position;
                Quaternion rotation = item.transform.rotation;
                Item itemData = item.item;
                int quantity = item.quantity;
                
                // Get container data for bags
                string containerData = "{}";
                if (itemData is Bag)
                {
                    var droppedStorage = item.GetComponent<InvDroppedStorageEquipment>();
                    if (droppedStorage != null)
                    {
                        containerData = droppedStorage.GetContainerSnapshot();
                    }
                }
                
                // Remove from tracking
                if (activeWorldItems.ContainsKey(itemId))
                {
                    activeWorldItems.Remove(itemId);
                }
                
                // Destroy the messy item
                GameObject oldObject = item.gameObject;
                Destroy(oldObject);
                
                // Spawn a clean version
                GameObject cleanItem = SpawnRestoredItem(itemData, position, rotation, itemDropping);
                if (cleanItem != null)
                {
                    cleanItem.name = $"Cleaned_{itemData.itemName}_{itemId.Substring(0, Mathf.Min(8, itemId.Length))}";
                    
                    // Parent to container if available
                    if (worldItemsContainer != null)
                    {
                        cleanItem.transform.SetParent(worldItemsContainer);
                    }
                    
                    // Set up the item pickup component
                    var cleanItemPickup = cleanItem.GetComponent<InvItemPickup>();
                    if (cleanItemPickup != null)
                    {
                        cleanItemPickup.SetItem(itemData, quantity);
                        
                        // Handle container data for bags
                        if (itemData is Bag && !string.IsNullOrEmpty(containerData) && containerData != "{}")
                        {
                            var droppedStorage = cleanItem.GetComponent<InvDroppedStorageEquipment>();
                            if (droppedStorage == null)
                            {
                                droppedStorage = cleanItem.AddComponent<InvDroppedStorageEquipment>();
                            }
                            droppedStorage.SetContainerSnapshot(containerData);
                        }
                        
                        // Add unique ID
                        var idComponent = cleanItem.GetComponent<ItemUniqueId>();
                        if (idComponent == null)
                        {
                            idComponent = cleanItem.AddComponent<ItemUniqueId>();
                        }
                        idComponent.UniqueId = itemId;
                        idComponent.WasStabilized = true;
                        idComponent.InitialPosition = position;
                        
                        // Register for tracking
                        RegisterWorldItem(cleanItemPickup);
                        
                        cleanedCount++;
                    }
                    else
                    {
                        Debug.LogError($"[PlayerProgression] Failed to set up cleaned item: {itemData.itemName}");
                        Destroy(cleanItem);
                    }
                }
                else
                {
                    Debug.LogError($"[PlayerProgression] Failed to spawn clean item: {itemData.itemName}");
                }
            }
        }
        
        Debug.Log($"[PlayerProgression] Cleaned up {cleanedCount} messy items");
        
        // Save the updated world items
        SaveWorldItems();
    }

    /// <summary>
    /// Manually trigger cleanup and restoration for testing
    /// </summary>
    public void CleanupDuplicatesAndRestore()
    {
        // Clean up saved data duplicates
        CleanupDuplicateWorldItems();
        
        // Get all existing items in scene
        var existingItems = FindObjectsOfType<InvItemPickup>();
        Debug.Log($"[PlayerProgression] Found {existingItems.Length} existing items in scene before cleanup");
        
        // Track positions of items to find duplicates
        Dictionary<string, List<InvItemPickup>> itemsByPosition = new Dictionary<string, List<InvItemPickup>>();
        
        foreach (var item in existingItems)
        {
            if (item == null || item.transform == null) continue;
            
            // Round position to reduce floating point issues
            string posKey = $"{Mathf.Round(item.transform.position.x * 10) / 10}_{Mathf.Round(item.transform.position.y * 10) / 10}_{Mathf.Round(item.transform.position.z * 10) / 10}";
            
            if (!itemsByPosition.ContainsKey(posKey))
            {
                itemsByPosition[posKey] = new List<InvItemPickup>();
            }
            
            itemsByPosition[posKey].Add(item);
        }
        
        // Destroy duplicate items at each position (keep the one with unique ID when possible)
        int destroyedCount = 0;
        foreach (var posGroup in itemsByPosition.Values)
        {
            if (posGroup.Count > 1)
            {
                Debug.Log($"[PlayerProgression] Found {posGroup.Count} scene duplicates at position {posGroup[0].transform.position}");
                
                // First, try to find the one with unique ID component
                InvItemPickup itemToKeep = null;
                foreach (var item in posGroup)
                {
                    var uniqueId = item.GetComponent<ItemUniqueId>();
                    if (uniqueId != null && !string.IsNullOrEmpty(uniqueId.UniqueId))
                    {
                        itemToKeep = item;
                        if (uniqueId.WasPlayerDropped)
                        {
                            // Prioritize player-dropped items
                            break;
                        }
                    }
                }
                
                // If no item with ID found, keep the first one
                if (itemToKeep == null)
                {
                    itemToKeep = posGroup[0];
                }
                
                // Destroy all but the one to keep
                foreach (var item in posGroup)
                {
                    if (item != itemToKeep)
                    {
                        Debug.Log($"[PlayerProgression] Destroying duplicate item: {item.gameObject.name}");
                        Destroy(item.gameObject);
                        destroyedCount++;
                    }
                }
            }
        }
        
        Debug.Log($"[PlayerProgression] Destroyed {destroyedCount} duplicate items in scene");
        
        // Refresh the active items tracking
        activeWorldItems.Clear();
        var remainingItems = FindObjectsOfType<InvItemPickup>();
        foreach (var item in remainingItems)
        {
            RegisterWorldItem(item);
        }
        
        // Save the current state
        SaveWorldItems();
    }

    // Version migration handlers
    private void MigrateToVersion1(ProgressionData data)
    {
        // Example migration from version 0 to 1
        data.saveFormatVersion = 1;
        
        // Any other data conversions would happen here
        
        // Ensure data integrity
        if (data.worldItems == null)
            data.worldItems = new List<WorldItemData>();
            
        if (data.pickedUpItemIds == null)
            data.pickedUpItemIds = new List<string>();
            
        Debug.Log("[PlayerProgression] Migration to version 1 complete");
    }

    // Backup corrupted save and create a new one (improved to preserve currency)
    private void HandleCorruptedSave(string originalPath, Exception error)
    {
        try {
            // Only attempt backup if the file exists
            if (File.Exists(originalPath))
            {
                // Create backup filename with timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string backupPath = originalPath + $".corrupted_{timestamp}.bak";
                
                // Copy the corrupted file as backup
                File.Copy(originalPath, backupPath);
                
                // Log the backup operation
                Debug.LogWarning($"[PlayerProgression] Corrupted save backed up to: {backupPath}");
                Debug.LogError($"[PlayerProgression] Save corruption details: {error.Message}");
                
                // Try to preserve currency before creating new save
                int preservedCurrency = 0;
                
                // Try to get currency from various backup sources
                if (PlayerPrefs.HasKey("emergency_currency_backup"))
                {
                    preservedCurrency = PlayerPrefs.GetInt("emergency_currency_backup", 0);
                    Debug.Log($"[PlayerProgression] Preserved currency from emergency backup: {preservedCurrency}");
                }
                else if (PlayerPrefs.HasKey("player_currency_last"))
                {
                    preservedCurrency = PlayerPrefs.GetInt("player_currency_last", 0);
                    Debug.Log($"[PlayerProgression] Preserved currency from last backup: {preservedCurrency}");
                }
                
                // Create a new save data
                progressionData = new ProgressionData();
                progressionData.saveFormatVersion = 1;
                progressionData.gameVersion = Application.version;
                progressionData.playerMoney = preservedCurrency; // Preserve currency
                
                // Save the new data
                SaveProgressionData();
                
                // Notify player of the issue
                if (NotificationManager.Instance != null)
                {
                    if (preservedCurrency > 0)
                    {
                        NotificationManager.Instance.ShowNotification($"Save file was corrupted and has been reset. Your currency ({preservedCurrency}) has been preserved. A backup of your previous save was created.");
                    }
                    else
                    {
                        NotificationManager.Instance.ShowNotification("Save file was corrupted and has been reset. A backup of your previous save was created.");
                    }
                }
            }
        }
        catch (Exception ex) {
            Debug.LogError($"[PlayerProgression] Failed to handle corrupted save: {ex.Message}");
        }
    }

    private void CleanupMemoryLeaks()
    {
        // Force cleanup of graphics resources to prevent memory leaks
        Debug.Log("[PlayerProgression] Cleaning up memory leaks...");

        // Force garbage collection
        System.GC.Collect();
        System.GC.WaitForPendingFinalizers();
        System.GC.Collect();

        // Unload unused assets
        Resources.UnloadUnusedAssets();

        Debug.Log("[PlayerProgression] Memory cleanup complete");
    }

    private void OnDestroy()
    {
        // Save the final currency state if PlayerStatus exists
        if (playerStatus != null)
        {
            Debug.Log($"[PlayerProgression] OnDestroy: Saving final currency state: {playerStatus.Currency}");
            progressionData.playerMoney = playerStatus.Currency;
            
            // Save to PlayerPrefs directly as a failsafe
            PlayerPrefs.SetInt("player_currency_last", playerStatus.Currency);
            PlayerPrefs.SetInt("emergency_currency_backup", playerStatus.Currency);
            PlayerPrefs.Save();
            
            // Try to save to the main save file too
            try {
                SaveProgressionDataOptimized();
            }
            catch (Exception e) {
                Debug.LogError($"[PlayerProgression] Failed to save currency on destroy: {e.Message}");
            }
        }
        
        // Clear the object pool
        worldItemDataPool.Clear();
        
        // Clear component caches
        itemUniqueIdCache.Clear();
        tempItemKeys.Clear();
        tempItemsToRemove.Clear();
        
        // Unsubscribe from events to prevent memory leaks
        // GameFlowManager integration removed - PlayerProgressionManager is now sole authority
        
        // Ensure we remove the application quitting callback
        Application.quitting -= SaveProgressionData;
        Application.quitting -= CleanupMemoryLeaks;
        
        // Cancel any repeating invokes
        CancelInvoke();
    }
    
    // Legacy method removed - PlayerProgressionManager is now sole authority for saves

    // Save player energy levels
    private void SavePlayerEnergyLevels()
    {
        if (progressionData == null) return;

        if (playerStatus != null)
        {
            // Store current energy values
            progressionData.playerCurrentEnergy = playerStatus.currentEnergy;

            if (verboseLogging) {
                Log($"[PlayerProgression] Saved player energy: {progressionData.playerCurrentEnergy}/{playerStatus.maxEnergy}");
            }
        }
    }

    // Add an entry to the pickup log and save it using object pooling
    private void AddPickupLogEntry(string itemId, string itemName, int quantity, bool isPickup = true)
    {
        PickupLogEntry entry;
        
        // Reuse an entry from the pool if available
        if (pickupLogEntryPool.Count > 0)
        {
            entry = pickupLogEntryPool.Dequeue();
            entry.id = itemId;
            entry.itemName = itemName;
            entry.quantity = quantity;
            entry.timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            entry.isPickup = isPickup;
        }
        else
        {
            // Create a new one if pool is empty
            entry = new PickupLogEntry(itemId, itemName, quantity, isPickup);
        }
        
        pickupLog.entries.Add(entry);
        pickupLogDirty = true;
    }
    
    // Save the pickup log to disk
    private void SavePickupLog()
    {
        if (pickupLog == null || pickupLog.entries.Count == 0) return;
        
        try
        {
            string jsonData = JsonUtility.ToJson(pickupLog);
            // Capture the path on the main thread
            string pickupLogPath = PickupLogFilePath;
            string encryptedData = EncryptString(jsonData);
            
            // Write log in a non-blocking way using System.IO.File instead of Unity's File class
            System.Threading.ThreadPool.QueueUserWorkItem(state => {
                try
                {
                    // Use the captured path instead of accessing PickupLogFilePath
                    File.AppendAllText(pickupLogPath, encryptedData + "\n");
                }
                catch (Exception e)
                {
                    // We can't directly log from a background thread, so just store the error
                    // and log it next time we're on the main thread
                    string errorMessage = e.Message;
                    lock(pickupLogErrorMessages) {
                        pickupLogErrorMessages.Add($"[PlayerProgression] Failed to save pickup log: {errorMessage}");
                    }
                }
            });
            
            ReturnLogEntriesToPool();
            if (verboseLogging) {
                Log($"[PlayerProgression] Queued {pickupLog.entries.Count} pickup log entries for saving");
            }
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Failed to prepare pickup log: {e.Message}");
        }
    }
    
    // Load and apply the pickup log
    private void LoadAndApplyPickupLog()
    {
        if (!File.Exists(PickupLogFilePath)) return;
        
        try
        {
            // Read all lines from the pickup log
            string[] logLines = File.ReadAllLines(PickupLogFilePath);
            
            int appliedCount = 0;
            
            foreach (string line in logLines)
            {
                if (string.IsNullOrEmpty(line)) continue;
                
                try
                {
                    string decryptedJson = DecryptString(line);
                    if (string.IsNullOrEmpty(decryptedJson)) continue;
                    
                    PickupLog logSection = JsonUtility.FromJson<PickupLog>(decryptedJson);
                    if (logSection == null || logSection.entries == null) continue;
                    
                    // Apply each entry to the progression data
                    foreach (var entry in logSection.entries)
                    {
                        if (entry.isPickup)
                        {
                            // It's a pickup event
                            if (!progressionData.pickedUpItemIds.Contains(entry.id))
                            {
                                progressionData.pickedUpItemIds.Add(entry.id);
                                appliedCount++;
                            }
                        }
                        else
                        {
                            // It's a drop event, remove from picked up list
                            if (progressionData.pickedUpItemIds.Contains(entry.id))
                            {
                                progressionData.pickedUpItemIds.Remove(entry.id);
                                appliedCount++;
                            }
                        }
                    }
                }
                catch (Exception lineEx)
                {
                    LogWarning($"[PlayerProgression] Failed to process pickup log line: {lineEx.Message}");
                }
            }
            
            if (verboseLogging || appliedCount > 0) {
                Log($"[PlayerProgression] Applied {appliedCount} pickup log entries");
            }
            
            // Clear the pickup log in memory since we've applied all entries
            pickupLog.entries.Clear();
        }
        catch (Exception e)
        {
            LogError($"[PlayerProgression] Failed to load pickup log: {e.Message}");
        }
    }
    
    // Consolidate the pickup log into the main save
    private void ConsolidateSaves()
    {
        // Make sure we have the latest state
        LoadAndApplyPickupLog();
        
        // Save player position and item velocities
        SavePlayerPosition();
        SaveItemVelocities();
        
        // Save inventory and world items
        SaveInventoryAndEquipment();
        SaveWorldItems();
        SaveHeavyObjects();
        // Save platforms
        if (activePlatforms.Count > 0)
        {
            foreach (var kv in activePlatforms)
            {
                if (kv.Value != null)
                {
                    UpdateKinematicPlatformData(kv.Value);
                }
            }
        }
        
        // Full save to the main file
        SaveProgressionDataOptimized();
        
        // Clear the pickup log file since its data is now in the main save
        try
        {
            File.WriteAllText(PickupLogFilePath, string.Empty);
            pickupLog.entries.Clear();
            pickupLogDirty = false;
            
            if (verboseLogging) {
                Log("[PlayerProgression] Consolidated pickup log into main save");
            }
        }
        catch (Exception e)
        {
            LogWarning($"[PlayerProgression] Failed to clear pickup log file: {e.Message}");
        }
        
        // Reset the timer
        timeSinceLastFullSave = 0f;
    }

    // Return log entries to the pool when done with them
    private void ReturnLogEntriesToPool()
    {
        // After we've saved the log, return all entries to the pool
        foreach (var entry in pickupLog.entries)
        {
            pickupLogEntryPool.Enqueue(entry);
        }
        pickupLog.entries.Clear();
    }

    private IEnumerator InitializePlayerCurrencyDelayed()
    {
        // If we have CurrencyManager, we don't need to do this anymore
        if (currencyManager != null)
        {
            Log("[PlayerProgression] Using CurrencyManager for currency management");
            
            // Instead of skipping, let's ensure currency is properly initialized
            int currentBestCurrency = 0;
            
            // First check if we already have a value in progressionData
            if (progressionData != null)
            {
                currentBestCurrency = progressionData.playerMoney;
                if (verboseLogging) {
                    Log($"[PlayerProgression] Currency from progression data: {currentBestCurrency}");
                }
            }
            
            // Check if CurrencyManager has a higher value
            if (currencyManager.CurrentCurrency > currentBestCurrency)
            {
                currentBestCurrency = currencyManager.CurrentCurrency;
                if (verboseLogging) {
                    Log($"[PlayerProgression] Using higher currency from CurrencyManager: {currentBestCurrency}");
                }
            }
            
            // Update PlayerStatus directly
            if (playerStatus != null)
            {
                playerStatus.ForceSetCurrency(currentBestCurrency);
                Log($"[PlayerProgression] Set player currency to: {currentBestCurrency}");
                
                // Wait a frame for everything to process
                yield return null;
                
                // Force CurrencyManager to notify all listeners
                currencyManager.NotifyAllListeners();
            }
            else
            {
                // If no PlayerStatus, at least update CurrencyManager
                currencyManager.SetCurrency(currentBestCurrency);
                Log($"[PlayerProgression] Set CurrencyManager to: {currentBestCurrency}");
            }
            
            yield break;
        }
        
        // Original implementation for when CurrencyManager isn't available
        // This is legacy code that should rarely if ever be used
        
        // Run immediately instead of waiting, but still use a coroutine to avoid blocking
        yield return null;
        
        // Rest of legacy implementation...
        // (existing code remains unchanged)
    }

    // Simplified to work with CurrencyManager
    public void SavePlayerCurrency()
    {
        // We use CurrencyManager as source of truth, so just update ProgressionData
        if (progressionData == null) 
        {
            LogError("[PlayerProgression] Cannot save currency: progressionData is null!");
            return;
        }
        
        // Get currency from CurrencyManager
        if (currencyManager != null)
        {
            int currentCurrency = currencyManager.CurrentCurrency;
            progressionData.playerMoney = currentCurrency;
            Log($"[PlayerProgression] Saving currency from CurrencyManager: {currentCurrency}");
            
            // Use the optimized save method which includes better error handling
            SaveProgressionDataOptimized();
            
            // Create multiple backups in PlayerPrefs for redundancy
            PlayerPrefs.SetInt("player_currency_last", currentCurrency);
            PlayerPrefs.SetInt("emergency_currency_backup", currentCurrency);
            PlayerPrefs.SetInt("player_currency_final", currentCurrency);
            PlayerPrefs.SetInt("emergency_currency_final", currentCurrency);
            PlayerPrefs.Save();
            
            if (verboseLogging) {
                Log($"[PlayerProgression] Created multiple currency backups: {currentCurrency}");
            }
        }
        else
        {
            LogError("[PlayerProgression] CurrencyManager not found when saving currency!");
        }
        }
    
    /// <summary>
    /// Save the currently selected tool from the ToolSelectionManager
    /// </summary>
    public void SaveToolSelection(string toolName, string originalItemName = "")
    {
        if (progressionData == null) return;
        
        progressionData.selectedToolName = toolName ?? "Hands";
        progressionData.selectedToolItemName = originalItemName ?? "";
        
        if (verboseLogging)
        {
            Log($"[PlayerProgression] Saved tool selection: {toolName} (item: {originalItemName})");
        }
    }
    
    /// <summary>
    /// Get the saved tool selection data
    /// </summary>
    public (string toolName, string itemName) GetSavedToolSelection()
    {
        if (progressionData == null)
            return ("Hands", "");
            
        return (progressionData.selectedToolName ?? "Hands", progressionData.selectedToolItemName ?? "");
    }
    
    // Save player position and velocity in real-time
    // Fixed: Now saves every 0.25s regardless of movement to handle force quits (like Alt+F4)
    // Previously only saved when player moved significantly, causing force quits to lose position
    private void SavePlayerPosition()
    {
        // Do not save while restoration is in progress; avoids clobbering with default/zero pose on load
        if (IsRestoring)
        {
            if (verboseLogging)
            {
                Log("[PlayerProgression] Skipping SavePlayerPosition during restoration");
            }
            return;
        }
        // Also skip in the first frames immediately after restoration to avoid late spawns overwriting
        if (lastRestorationTime > 0 && Time.time - lastRestorationTime < 1.0f)
        {
            if (verboseLogging)
            {
                Log("[PlayerProgression] Skipping SavePlayerPosition right after restoration window");
            }
            return;
        }
        // During application quit, perform an extra sanity check to avoid saving spawn fallback positions
        bool isQuitting = isQuittingApp;
        if (progressionData == null || playerTransform == null) 
        {
            LogError("[PlayerProgression] SavePlayerPosition failed - null references!");
            return;
        }      

        // Choose best source for position (use ragdoll when tumbling)
        Vector3 currentPosition = playerTransform.position;
        string positionSource = "player";
        var rag = ragdollTumbleSystem != null ? ragdollTumbleSystem : FindObjectOfType<RagdollTumbleSystem>();
        if (rag != null)
        {
            ragdollTumbleSystem = rag;
            if (rag.IsTumbling && rag.ragdollObject != null && rag.ragdollObject.activeInHierarchy)
            {
                currentPosition = rag.ragdollObject.transform.position;
                positionSource = "ragdoll";
            }
        }

        // Guard: avoid saving suspicious positions that likely come from spawn/reset/teleport glitches
        bool nearOrigin = currentPosition.sqrMagnitude < (3f * 3f);
        bool havePrev = progressionData.playerPositionTimestamp > 0f && progressionData.playerPosition != Vector3.zero;
        bool haveLastSaved = lastSavedPlayerPosition != Vector3.zero;
        float timeSincePrevSave = havePrev ? (Time.time - progressionData.playerPositionTimestamp) : float.MaxValue;
        bool recentPrevSave = havePrev && timeSincePrevSave < 20f;
        // tighter jump threshold so we catch unintended teleports back to spawn
        bool largeJumpFromPrev = havePrev && Vector3.Distance(currentPosition, progressionData.playerPosition) > 25f;
        bool largeJumpFromLast = haveLastSaved && Vector3.Distance(currentPosition, lastSavedPlayerPosition) > 25f;
        bool isTumblingNow = rag != null && rag.IsTumbling;
        if (!isTumblingNow)
        {
            if (isQuitting && nearOrigin && (largeJumpFromPrev || largeJumpFromLast || recentPrevSave))
            {
                Vector3 fallback = havePrev ? progressionData.playerPosition : (haveLastSaved ? lastSavedPlayerPosition : currentPosition);
                if (fallback != currentPosition)
                {
                    if (verboseLogging)
                    {
                        Debug.LogWarning($"[PlayerProgression] Suspicious near-origin position detected (source: {positionSource}, pos: {currentPosition}, recentPrevSave={recentPrevSave}). Using fallback: {fallback}");
                    }
                    currentPosition = fallback;
                    positionSource = havePrev ? "previous-saved" : "last-saved";
                }
            }
            else if (isQuitting && (largeJumpFromPrev || largeJumpFromLast) && recentPrevSave)
            {
                Vector3 fallback = havePrev ? progressionData.playerPosition : (haveLastSaved ? lastSavedPlayerPosition : currentPosition);
                if (fallback != currentPosition)
                {
                    if (verboseLogging)
                    {
                        Debug.LogWarning($"[PlayerProgression] Large unexpected jump detected (source: {positionSource}, fromPrev={largeJumpFromPrev}, fromLast={largeJumpFromLast}, recentPrevSave={recentPrevSave}). Using fallback: {fallback}");
                    }
                    currentPosition = fallback;
                    positionSource = havePrev ? "previous-saved" : "last-saved";
                }
            }
        }
        if (currentPosition == Vector3.zero)
        {
            LogError($"[PlayerProgression] WARNING: Saving position as 0,0,0 (source: {positionSource})! Stack trace: {System.Environment.StackTrace}");
        }

        // ALWAYS save position every time this is called (like the item system does)
        // This ensures force quits don't lose recent position data
        bool shouldSave = true;
        
        // Movement detection for logging and optimization
        bool hasMovedSignificantly = Vector3.Distance(currentPosition, lastSavedPlayerPosition) > 0.5f;
        bool isMovingFast = false;
        bool hasAnyMovement = hasMovedSignificantly;
        
        if (playerController != null && playerController.Motor != null)
        {
            Vector3 currentVelocity = playerController.Motor.BaseVelocity;
            isMovingFast = currentVelocity.magnitude > 2f;
            hasAnyMovement = hasAnyMovement || currentVelocity.magnitude > 0.1f;
        }
        
        // Small optimization: if player hasn't moved at all and position is already saved, skip save
        // But still save periodically (this method is called every 0.5s) to handle force quits
        if (!hasAnyMovement && Vector3.Distance(currentPosition, lastSavedPlayerPosition) < 0.01f)
        {
                         // Only skip if we've saved recently (to ensure force quit protection)
             if (progressionData.playerPositionTimestamp > 0 && 
                 Time.time - progressionData.playerPositionTimestamp < 1f)
             {
                 shouldSave = false;
                 if (verboseLogging) {
                     Log("[PlayerProgression] Skipping position save - no movement and recent save exists");
                 }
             }
        }
        
        if (shouldSave)
        {
            progressionData.playerPosition = currentPosition;
            // If saving from ragdoll, try to keep rotation yaw from ragdoll; otherwise use player transform
            if (positionSource == "ragdoll" && ragdollTumbleSystem != null && ragdollTumbleSystem.ragdollObject != null)
            {
                var ry = ragdollTumbleSystem.ragdollObject.transform.rotation.eulerAngles.y;
                progressionData.playerRotation = Quaternion.Euler(0f, ry, 0f);
            }
            else
            {
                progressionData.playerRotation = playerTransform.rotation;
            }
            progressionData.playerSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            progressionData.playerPositionTimestamp = Time.time;

            // Save riding state on platforms
            SavePlayerPlatformAttachment();
            
            // Also save camera rotation if available
            if (_cachedFpsCamera == null)
            {
                _cachedFpsCamera = Camera.main != null ? Camera.main.GetComponent<FPSCharacterCamera>() : null;
            }
            var fpsCamera = _cachedFpsCamera;
            if (fpsCamera != null)
            {
                var camTransform = fpsCamera.transform;
                Vector3 angles = camTransform.eulerAngles;
                progressionData.cameraRotationX = angles.x > 180f ? angles.x - 360f : angles.x; // Normalize pitch
                progressionData.cameraRotationY = angles.y;
                
                if (verboseLogging) {
                    Log($"[PlayerProgression] Saved camera rotation during position save: pitch={progressionData.cameraRotationX}, yaw={progressionData.cameraRotationY}");
                }
            }
            
            // Save velocity data if available
            if (playerController != null && playerController.Motor != null)
            {
                Vector3 currentVelocity = playerController.Motor.BaseVelocity;
                bool isGroundedNow = playerController.Motor.GroundingStatus.IsStableOnGround;
                bool shouldSnapshotVelocity = !isGroundedNow || currentVelocity.magnitude > 0.5f || currentVelocity.y < -0.5f;

                if (shouldSnapshotVelocity)
                {
                    progressionData.playerVelocity = currentVelocity;
                    progressionData.playerIsGrounded = isGroundedNow;
                    progressionData.playerVelocityTimestamp = Time.time;

                    // Track falling state for continuity
                    var fallDamageSystem = playerTransform.GetComponent<FallDamageSystem>();
                    if (fallDamageSystem != null)
                    {
                        progressionData.playerWasFalling = fallDamageSystem.IsFalling();
                        progressionData.playerFallStartHeight = fallDamageSystem.GetFallStartHeight();
                    }
                }
            }
            
            lastSavedPlayerPosition = currentPosition;
            
            // Save immediately to PlayerPrefs for quick access on force quit
            PlayerPrefs.SetFloat("player_pos_x", currentPosition.x);
            PlayerPrefs.SetFloat("player_pos_y", currentPosition.y);
            PlayerPrefs.SetFloat("player_pos_z", currentPosition.z);
            PlayerPrefs.SetString("player_scene", progressionData.playerSceneName);
            
            // Also save velocity to PlayerPrefs for immediate access
            if (playerController != null && playerController.Motor != null)
            {
                Vector3 velocity = playerController.Motor.BaseVelocity;
                bool isGroundedNow = playerController.Motor.GroundingStatus.IsStableOnGround;
                bool shouldSnapshotVelocity = !isGroundedNow || velocity.magnitude > 0.5f || velocity.y < -0.5f;
                if (shouldSnapshotVelocity)
                {
                    PlayerPrefs.SetFloat("player_vel_x", velocity.x);
                    PlayerPrefs.SetFloat("player_vel_y", velocity.y);
                    PlayerPrefs.SetFloat("player_vel_z", velocity.z);
                    PlayerPrefs.SetInt("player_grounded", isGroundedNow ? 1 : 0);
                    PlayerPrefs.SetFloat("player_vel_timestamp", Time.time);
                    // Persist falling state hints for edge-case resumes
                    PlayerPrefs.SetInt("player_was_falling", progressionData.playerWasFalling ? 1 : 0);
                    PlayerPrefs.SetFloat("player_fall_start_y", progressionData.playerFallStartHeight);
                }
            }
            
            PlayerPrefs.Save(); // Force immediate save to disk
            lastCommittedSavedPosition = currentPosition;
            lastCommittedPositionSource = positionSource;
            if (verboseLogging)
            {
                Debug.Log($"[PlayerProgression] Committed save position from {positionSource}: {currentPosition}");
            }
            
            if (verboseLogging) {
                string movementInfo = hasMovedSignificantly ? " (moved)" : "";
                string velocityInfo = isMovingFast ? " (fast)" : "";
                Log($"[PlayerProgression] Saved player position from {positionSource}: {currentPosition} and velocity: {progressionData.playerVelocity} in scene {progressionData.playerSceneName}{movementInfo}{velocityInfo}");
            }

            // GameFlowManager integration removed – PlayerProgressionManager is now sole authority for player position
        }
    }
    
    // Save item velocities in real-time
    private void SaveItemVelocities()
    {
        if (progressionData == null || activeWorldItems == null) return;
        
        int updatedCount = 0;
        
        foreach (var kvp in activeWorldItems)
        {
            var itemPickup = kvp.Value;
            if (itemPickup == null) continue;
            
            var rigidbody = itemPickup.GetComponent<Rigidbody>();
            if (rigidbody == null) continue;
            
            // Only save velocity if the item is moving
            if (rigidbody.linearVelocity.magnitude > 0.1f || rigidbody.angularVelocity.magnitude > 0.1f)
            {
                // Find the corresponding world item data
                var worldItemData = progressionData.worldItems.FirstOrDefault(item => item.id == kvp.Key);
                if (worldItemData != null)
                {
                    worldItemData.velocity = rigidbody.linearVelocity;
                    worldItemData.angularVelocity = rigidbody.angularVelocity;
                    worldItemData.isKinematic = rigidbody.isKinematic;
                    worldItemData.velocityTimestamp = Time.time;
                    
                    updatedCount++;
                }
            }
        }
        
        if (verboseLogging && updatedCount > 0) {
            Log($"[PlayerProgression] Saved velocity for {updatedCount} moving items");
        }
    }

    // Capture if the player is currently grounded on a kinematic platform and save attachment data
    private void SavePlayerPlatformAttachment()
    {
        if (progressionData == null || playerController == null || playerController.Motor == null) return;

        var motor = playerController.Motor;
        var attached = motor.AttachedRigidbodyOverride ?? (motor.GroundingStatus.IsStableOnGround ? motor.GroundingStatus.GroundCollider?.attachedRigidbody : null);
        if (attached != null)
        {
            var platformPersist = attached.GetComponent<KinematicPlatformPersistence>();
            if (platformPersist != null)
            {
                // Player local offset relative to platform
                Vector3 local = platformPersist.transform.InverseTransformPoint(playerTransform.position);
                progressionData.playerWasOnPlatform = true;
                progressionData.playerPlatformId = platformPersist.UniqueId;
                progressionData.playerLocalOffsetOnPlatform = local;
                // NEW: Save all passengers on this platform
                SavePlatformPassengers(platformPersist);
                return;
            }
        }

        // Not on a platform
        progressionData.playerWasOnPlatform = false;
        progressionData.playerPlatformId = "";
    }

    // NEW: Save all passengers present on the platform's passenger zones
    private void SavePlatformPassengers(KinematicPlatformPersistence platformPersist)
    {
        if (platformPersist == null || progressionData == null) return;

        // Find or create the platform data
        var platformData = progressionData.kinematicPlatforms
            .FirstOrDefault(p => p.id == platformPersist.UniqueId);

        if (platformData == null)
        {
            platformData = new KinematicPlatformData
            {
                id = platformPersist.UniqueId,
                objectName = platformPersist.ObjectName,
                sceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name,
                currentWorldPosition = platformPersist.transform.position,
            };
            progressionData.kinematicPlatforms.Add(platformData);
        }

        platformData.passengers.Clear();

        // Save player as passenger if applicable
        if (progressionData.playerWasOnPlatform && progressionData.playerPlatformId == platformPersist.UniqueId)
        {
            platformData.passengers.Add(new PassengerData
            {
                id = "player",
                type = PassengerType.Player,
                localPosition = progressionData.playerLocalOffsetOnPlatform,
                localRotation = Quaternion.Inverse(platformPersist.transform.rotation) * playerTransform.rotation,
                velocity = (playerController != null && playerController.Motor != null) ? playerController.Motor.BaseVelocity : Vector3.zero,
                angularVelocity = Vector3.zero,
                wasKinematic = true,
                originalConstraints = RigidbodyConstraints.None,
            });
        }

        var zones = platformPersist.GetComponentsInChildren<KinematicPlatformPassengerZone>(true);
        foreach (var zone in zones)
        {
            if (zone == null) continue;
            var col = zone.GetComponent<Collider>();
            if (col == null) continue;

            var b = col.bounds;
            Collider[] overlapped = Physics.OverlapBox(b.center, b.extents, col.transform.rotation, ~0, QueryTriggerInteraction.Ignore);
            for (int i = 0; i < overlapped.Length; i++)
            {
                var ov = overlapped[i];
                if (ov == null || ov.attachedRigidbody == null) continue;
                var rb = ov.attachedRigidbody;

                // Skip the platform itself
                if (rb.gameObject == platformPersist.gameObject) continue;

                // Skip player (already saved)
                if (rb.GetComponentInParent<FPSCharacterController>() != null || rb.GetComponent<KinematicCharacterMotor>() != null)
                {
                    continue;
                }

                // If it's an item
                var itemPickup = rb.GetComponent<InvItemPickup>();
                if (itemPickup != null)
                {
                    // Prefer a stable item id used by world item tracking
                    string stableItemId = GetItemId(itemPickup);
                    platformData.passengers.Add(new PassengerData
                    {
                        id = stableItemId,
                        type = PassengerType.Item,
                        localPosition = platformPersist.transform.InverseTransformPoint(rb.position),
                        localRotation = Quaternion.Inverse(platformPersist.transform.rotation) * rb.rotation,
                        itemName = itemPickup.item != null ? itemPickup.item.itemName : itemPickup.name,
                        velocity = rb.linearVelocity,
                        angularVelocity = rb.angularVelocity,
                        wasKinematic = rb.isKinematic,
                        originalConstraints = rb.constraints,
                    });
                    continue;
                }

                // Generic rigidbody
                platformData.passengers.Add(new PassengerData
                {
                    id = rb.GetInstanceID().ToString(),
                    type = PassengerType.Other,
                    localPosition = platformPersist.transform.InverseTransformPoint(rb.position),
                    localRotation = Quaternion.Inverse(platformPersist.transform.rotation) * rb.rotation,
                    velocity = rb.linearVelocity,
                    angularVelocity = rb.angularVelocity,
                    wasKinematic = rb.isKinematic,
                    originalConstraints = rb.constraints,
                });
            }
        }

        if (verboseLogging)
        {
            Log($"[PlayerProgression] Saved {platformData.passengers.Count} passengers on platform {platformPersist.UniqueId}");
        }
    }

    // Restore player position and velocity from save if available
    private void RestorePlayerPosition()
    {
        if (progressionData == null || playerTransform == null) return;
        
        string currentScene = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
        Vector3 positionToRestore = Vector3.zero;
        Quaternion rotationToRestore = Quaternion.identity;
        Vector3 velocityToRestore = Vector3.zero;
        bool isGroundedToRestore = true;
        bool shouldRestore = false;
        bool hasVelocityData = false;
        bool wasFallingToRestore = false;
        float fallStartHeightToRestore = 0f;
        bool savedAirborneToRestore = false;
        bool hasCameraRotationData = false;
        float cameraRotationX = 0f;
        float cameraRotationY = 0f;
        
        string restoreSource = "";
        // First try to restore from progression data
        if (progressionData.playerPosition != Vector3.zero && 
            progressionData.playerSceneName == currentScene &&
            progressionData.playerPositionTimestamp > 0)
        {
            positionToRestore = progressionData.playerPosition;
            rotationToRestore = progressionData.playerRotation;
            shouldRestore = true;
            restoreSource = "progressionData";
            
            // Check if we have recent velocity data OR we know we were falling when saved
            bool velRecent = progressionData.playerVelocityTimestamp > 0 &&
                              (Time.time - progressionData.playerVelocityTimestamp) < 10f;
            if (velRecent || progressionData.playerWasFalling || !progressionData.playerIsGrounded)
            {
                velocityToRestore = progressionData.playerVelocity;
                // If we saved while falling, force ungrounded on restore
                isGroundedToRestore = (progressionData.playerWasFalling || !progressionData.playerIsGrounded) ? false : progressionData.playerIsGrounded;
                hasVelocityData = true;
                wasFallingToRestore = progressionData.playerWasFalling;
                fallStartHeightToRestore = progressionData.playerFallStartHeight;
                savedAirborneToRestore = !progressionData.playerIsGrounded;
            }
            
            // Check if we have camera rotation data
            if (progressionData.playerPositionTimestamp > 0)
            {
                cameraRotationX = progressionData.cameraRotationX;
                cameraRotationY = progressionData.cameraRotationY;
                hasCameraRotationData = true;
            }
            
            if (verboseLogging) {
                Log($"[PlayerProgression] Using position from progression data: {positionToRestore}");
            }
        }
        // Fall back to PlayerPrefs if progression data isn't available
        else if (PlayerPrefs.HasKey("player_pos_x") && PlayerPrefs.HasKey("player_pos_y") && 
                PlayerPrefs.HasKey("player_pos_z") && PlayerPrefs.HasKey("player_scene"))
        {
            float playerPosX = PlayerPrefs.GetFloat("player_pos_x");
            float playerPosY = PlayerPrefs.GetFloat("player_pos_y");
            float playerPosZ = PlayerPrefs.GetFloat("player_pos_z");
            string playerSceneName = PlayerPrefs.GetString("player_scene");
            
            // Check if the scene name matches the current scene
            if (currentScene == playerSceneName)
            {
                positionToRestore = new Vector3(playerPosX, playerPosY, playerPosZ);
                rotationToRestore = progressionData.playerRotation;
                shouldRestore = true;
                restoreSource = "PlayerPrefs";
                
                // Check for velocity data in PlayerPrefs
                if (PlayerPrefs.HasKey("player_vel_x") && PlayerPrefs.HasKey("player_vel_y") && 
                    PlayerPrefs.HasKey("player_vel_z") && PlayerPrefs.HasKey("player_vel_timestamp"))
                {
                    float velTimestamp = PlayerPrefs.GetFloat("player_vel_timestamp");
                    bool velRecent = (Time.time - velTimestamp) < 10f;
                    int wasFallingPref = PlayerPrefs.GetInt("player_was_falling", 0);
                    int groundedPref = PlayerPrefs.GetInt("player_grounded", 1);
                    if (velRecent || wasFallingPref == 1 || groundedPref == 0) // Restore if recent OR falling OR airborne
                    {
                        velocityToRestore = new Vector3(
                            PlayerPrefs.GetFloat("player_vel_x"),
                            PlayerPrefs.GetFloat("player_vel_y"),
                            PlayerPrefs.GetFloat("player_vel_z")
                        );
                        isGroundedToRestore = (wasFallingPref == 1 || groundedPref == 0) ? false : groundedPref == 1;
                        hasVelocityData = true;
                        wasFallingToRestore = wasFallingPref == 1;
                        fallStartHeightToRestore = PlayerPrefs.GetFloat("player_fall_start_y", 0f);
                        savedAirborneToRestore = groundedPref == 0;
                    }
                }
                
                // Check for camera rotation data in PlayerPrefs
                if (PlayerPrefs.HasKey("camera_pitch") && PlayerPrefs.HasKey("camera_yaw"))
                {
                    cameraRotationX = PlayerPrefs.GetFloat("camera_pitch");
                    cameraRotationY = PlayerPrefs.GetFloat("camera_yaw");
                    hasCameraRotationData = true;
                }
                
                if (verboseLogging) {
                    Log($"[PlayerProgression] Using position from PlayerPrefs: {positionToRestore}");
                }
            }
            else
            {
                Debug.Log($"[PlayerProgression] Skipping restore due to scene mismatch: savedScene={playerSceneName}, currentScene={currentScene}");
            }
        }
        
        // Final fallback: if no restore selected but player is at or very near origin, try PlayerPrefs regardless of scene
        if (!shouldRestore)
        {
            Vector3 current = playerTransform.position;
            if (current.sqrMagnitude < 0.25f &&
                PlayerPrefs.HasKey("player_pos_x") && PlayerPrefs.HasKey("player_pos_y") && PlayerPrefs.HasKey("player_pos_z"))
            {
                float px = PlayerPrefs.GetFloat("player_pos_x");
                float py = PlayerPrefs.GetFloat("player_pos_y");
                float pz = PlayerPrefs.GetFloat("player_pos_z");
                positionToRestore = new Vector3(px, py, pz);
                rotationToRestore = progressionData.playerRotation;
                shouldRestore = true;
                restoreSource = "PlayerPrefs (fallback-any-scene)";
                if (verboseLogging)
                {
                    Debug.Log($"[PlayerProgression] Fallback restore from PlayerPrefs due to near-origin spawn. pos={positionToRestore}");
                }
            }
        }

        // Apply the restoration if we found a valid position
        if (shouldRestore)
        {
            if (positionToRestore == Vector3.zero)
            {
                Debug.LogWarning($"[PlayerProgression] Restoring to 0,0,0 from {restoreSource} - check tumble save path and PlayerPrefs.");
            }
            playerTransform.position = positionToRestore;
            playerTransform.rotation = rotationToRestore;
            if (verboseLogging)
            {
                Debug.Log($"[PlayerProgression] Restored player position from {restoreSource}: {positionToRestore} in scene {currentScene}");
            }
            
            // Restore velocity if we have the data and the player controller
            if (hasVelocityData && playerController != null && playerController.Motor != null)
            {
                // Use StartCoroutine to wait a frame before applying velocity
                StartCoroutine(RestorePlayerVelocityDelayed(velocityToRestore, isGroundedToRestore, wasFallingToRestore, fallStartHeightToRestore, savedAirborneToRestore));
            }
            
            // Restore camera rotation if we have the data
            if (hasCameraRotationData)
            {
                StartCoroutine(RestoreCameraRotationDelayed(cameraRotationX, cameraRotationY));
            }
            
            Log($"[PlayerProgression] Restored player position: {positionToRestore} in scene {currentScene}");
            if (hasVelocityData) {
                Log($"[PlayerProgression] Will restore player velocity: {velocityToRestore} (grounded: {isGroundedToRestore})");
            }
            if (hasCameraRotationData) {
                Log($"[PlayerProgression] Will restore camera rotation: pitch={cameraRotationX}, yaw={cameraRotationY}");
            }

            // Mark as restored so we don't try again
            playerPositionRestored = true;

            // Player platform attachment is handled automatically in TryAttachPlayerToPlatformIfSaved
        }
        else
        {
            Debug.Log($"[PlayerProgression] No position restored. currentScene={currentScene}, savedScene={progressionData.playerSceneName}, savedPos={progressionData.playerPosition}");
        }
    }

    private IEnumerator RestorePlayerOnPlatformNextFrames()
    {
        // Engage platform hold immediately to prevent drift before placement
        string idEarly = progressionData.playerPlatformId;
        var scenePlatformsEarly = FindObjectsOfType<KinematicPlatformPersistence>();
        KinematicPlatformPersistence foundEarly = null;
        foreach (var p in scenePlatformsEarly)
        {
            if (p != null && p.UniqueId == idEarly) { foundEarly = p; break; }
        }
        if (foundEarly != null && foundEarly.Platform != null)
        {
            foundEarly.Platform.SetHoldForPlayerRestore(true);
        }
        // Allow one frame for all transforms to settle under hold
        yield return null;

        string id = progressionData.playerPlatformId;
        var scenePlatforms = FindObjectsOfType<KinematicPlatformPersistence>();
        KinematicPlatformPersistence found = null;
        foreach (var p in scenePlatforms)
        {
            if (p != null && p.UniqueId == id)
            {
                found = p; break;
            }
        }
        if (found == null || playerController == null || playerController.Motor == null)
        {
            MarkRestorationComplete();
            yield break;
        }

        var platform = found.Platform;
        if (platform == null)
        {
            MarkRestorationComplete();
            yield break;
        }

        // Platform is already held above; ensure it's still held
        platform.SetHoldForPlayerRestore(true);

        // Compute settle frames based on platform move speed
        float settleTime = CalculateSettleTime(platform.MoveSpeed);
        int settleFrames = Mathf.CeilToInt(Mathf.Max(0.75f, settleTime) / Time.fixedDeltaTime);

        // Restore all saved passengers; falls back to legacy behavior if no passenger list
        yield return RestoreAllPassengersOnPlatform(found, settleFrames);

        // Wait until critical passengers are detected inside zones and confirm attachment
        yield return WaitUntilPassengersInZones(found, settleTime + 1.0f, 3);

        // Confirm player attachment to platform rigidbody before release
        if (playerController != null && playerController.Motor != null)
        {
            var platformRb = found.GetComponent<Rigidbody>();
            if (platformRb != null && playerController.Motor.AttachedRigidbodyOverride != platformRb)
            {
                playerController.Motor.AttachedRigidbodyOverride = platformRb;
            }
        }

        // For extreme speeds (300+ m/s), do additional stability verification
        bool isExtremeSpeed = platform.MoveSpeed > 100f;
        if (isExtremeSpeed)
        {
            yield return VerifyPlayerStabilityAtExtremeSpeed(found, platform, 2.0f);
        }

        // Release platform hold and restoration gate
        platform.SetHoldForPlayerRestore(false);
        MarkRestorationComplete();
    }

    // Restore all saved passengers on a platform and keep them aligned for a settle duration
    private IEnumerator RestoreAllPassengersOnPlatform(KinematicPlatformPersistence platformPersist, int settleFrames)
    {
        if (platformPersist == null) yield break;

        var platformData = progressionData?.kinematicPlatforms
            ?.FirstOrDefault(p => p.id == platformPersist.UniqueId);

        if (platformData == null || platformData.passengers == null || platformData.passengers.Count == 0)
        {
            // Fall back to legacy behavior (maintain relative for a short period) if no passenger data saved
            yield return MaintainRelativePassengersOnPlatform(
                platformPersist,
                progressionData.playerLocalOffsetOnPlatform
            );
            yield break;
        }

        Transform platformTransform = platformPersist.transform;
        Transform anchor = (platformPersist.Platform != null && platformPersist.Platform.AttachmentAnchor != null)
            ? platformPersist.Platform.AttachmentAnchor
            : platformTransform;
        Rigidbody platformRb = platformPersist.GetComponent<Rigidbody>();
        PhysicsMover platformMover = platformPersist.GetComponent<PhysicsMover>();

        // Track original parents/physics for items we temporarily parent
        var rbRestoreMap = new Dictionary<Rigidbody, RBRestoreInfo>(platformData.passengers.Count);

        // Initial placement
        foreach (var passenger in platformData.passengers)
        {
            if (passenger.type == PassengerType.Player)
            {
                if (playerController != null && playerController.Motor != null)
                {
                    Vector3 wp = anchor.TransformPoint(passenger.localPosition);
                    Quaternion wr = anchor.rotation * passenger.localRotation;
                    playerController.Motor.SetPosition(wp, true);
                    if (playerTransform != null)
                    {
                        playerTransform.rotation = wr;
                    }
                    if (platformRb != null)
                    {
                        playerController.Motor.AttachedRigidbodyOverride = platformRb;
                    }
                }
            }
            else if (passenger.type == PassengerType.Item)
            {
                // Prefer stable world item ID matching if available
                InvItemPickup itemPickup = null;
                if (activeWorldItems != null && activeWorldItems.Count > 0)
                {
                    // Try match by saved id first
                    if (!string.IsNullOrEmpty(passenger.id) && activeWorldItems.TryGetValue(passenger.id, out var byId))
                    {
                        itemPickup = byId;
                    }
                    // Fallback to name match
                    if (itemPickup == null)
                    {
                        itemPickup = activeWorldItems.Values
                            .FirstOrDefault(it => it != null && it.item != null && it.item.itemName == passenger.itemName);
                    }
                }
                if (itemPickup != null)
                {
                    var rb = itemPickup.GetComponent<Rigidbody>();
                    if (rb != null)
                    {
                        // Record original state
                        rbRestoreMap[rb] = new RBRestoreInfo
                        {
                            parent = rb.transform.parent,
                            wasKinematic = rb.isKinematic,
                            constraints = rb.constraints,
                        };

                        // Parent to anchor during settle for perfect high-speed attachment
                        rb.transform.SetParent(anchor);
                        rb.isKinematic = true;
                        rb.constraints = RigidbodyConstraints.FreezeAll;
                        
                        // Set local pose to saved values so it rides perfectly
                        rb.transform.localPosition = passenger.localPosition;
                        rb.transform.localRotation = passenger.localRotation;
                    }
                }
            }
        }

        // Maintain alignment during settle
        for (int i = 0; i < settleFrames; i++)
        {
            foreach (var passenger in platformData.passengers)
            {
                if (passenger.type == PassengerType.Player)
                {
                    if (playerController != null && playerController.Motor != null)
                    {
                        Vector3 wp = anchor.TransformPoint(passenger.localPosition);
                        playerController.Motor.SetPosition(wp, true);
                        playerController.Motor.BaseVelocity = Vector3.zero;
                        if (platformRb != null && playerController.Motor.AttachedRigidbodyOverride != platformRb)
                        {
                            if (verboseLogging)
                            {
                                var cur = playerController.Motor.AttachedRigidbodyOverride;
                                Log($"[PlayerProgression] Motor.AttachedRigidbodyOverride deviated to {(cur == null ? "null" : cur.name)} during settle; re-attaching to {platformRb.name}");
                            }
                            playerController.Motor.AttachedRigidbodyOverride = platformRb;
                        }
                    }
                }
                // Items stay parented to anchor during settle; no additional positioning needed
            }

            yield return new WaitForFixedUpdate();
        }

        // Restore physics after settle, applying platform velocity so they keep momentum
        foreach (var passenger in platformData.passengers)
        {
            if (passenger.type == PassengerType.Item)
            {
                InvItemPickup itemPickup = null;
                if (activeWorldItems != null && activeWorldItems.Count > 0)
                {
                    if (!string.IsNullOrEmpty(passenger.id) && activeWorldItems.TryGetValue(passenger.id, out var byId))
                    {
                        itemPickup = byId;
                    }
                    if (itemPickup == null)
                    {
                        itemPickup = activeWorldItems.Values
                            .FirstOrDefault(it => it != null && it.item != null && it.item.itemName == passenger.itemName);
                    }
                }
                if (itemPickup != null)
                {
                    var rb = itemPickup.GetComponent<Rigidbody>();
                    if (rb != null)
                    {
                        if (rbRestoreMap.TryGetValue(rb, out var info))
                        {
                            // Compute velocity from platform motion
                            Vector3 platformVel = platformMover != null ? platformMover.Velocity : Vector3.zero;
                            Vector3 angularVel = platformMover != null ? platformMover.AngularVelocity : Vector3.zero;
                            Vector3 rel = rb.position - anchor.position;
                            Vector3 worldVel = platformVel + Vector3.Cross(angularVel, rel);

                            // Unparent and restore physics state
                            rb.transform.SetParent(info.parent);
                            rb.isKinematic = info.wasKinematic;
                            rb.constraints = info.constraints;

                            if (!rb.isKinematic)
                            {
                                rb.linearVelocity = worldVel;
                                rb.angularVelocity = angularVel;
                            }
                        }
                    }
                }
            }
        }
    }

    // Keep player and rigidbodies inside platform passenger zones exactly relative for a brief period after load
    private IEnumerator MaintainRelativePassengersOnPlatform(KinematicPlatformPersistence platformPersist, Vector3 playerLocalOffset)
    {
        if (platformPersist == null) yield break;

        // Collect current occupants within passenger zones and record their local offsets
        var platformTransform = platformPersist.transform;
        var platformRb = platformPersist.GetComponent<Rigidbody>();
        var zones = platformPersist.GetComponentsInChildren<KinematicPlatformPassengerZone>(true);
        var rbToLocal = new System.Collections.Generic.Dictionary<Rigidbody, Vector3>(32);

        for (int z = 0; z < zones.Length; z++)
        {
            var zone = zones[z];
            if (zone == null) continue;
            var col = zone.GetComponent<Collider>();
            if (col == null) continue;

            // Approximate overlap using the collider bounds
            var b = col.bounds;
            Collider[] overlapped = Physics.OverlapBox(b.center, b.extents, col.transform.rotation, ~0, QueryTriggerInteraction.Ignore);
            for (int i = 0; i < overlapped.Length; i++)
            {
                var ov = overlapped[i];
                if (ov == null || ov.attachedRigidbody == null) continue;
                var rb = ov.attachedRigidbody;
                if (rb == null) continue;
                if (rbToLocal.ContainsKey(rb)) continue;
                if (rb.gameObject == platformPersist.gameObject) continue;
                rbToLocal[rb] = platformTransform.InverseTransformPoint(rb.position);
            }
        }

        // For a handful of frames, keep positions relative while other systems finish restoring
        const int frames = 20;
        KinematicCharacterMotor motor = playerController != null ? playerController.Motor : null;
        Rigidbody prevOverride = null;
        if (motor != null && platformRb != null)
        {
            prevOverride = motor.AttachedRigidbodyOverride;
            motor.AttachedRigidbodyOverride = platformRb;
        }
        for (int f = 0; f < frames; f++)
        {
            // Maintain player exactly at saved local offset
            if (playerController != null && playerController.Motor != null)
            {
                Vector3 wp = platformTransform.TransformPoint(playerLocalOffset);
                playerController.Motor.SetPosition(wp, true);
            }

            // Maintain rigidbodies at their captured local offsets
            foreach (var kv in rbToLocal)
            {
                var rb = kv.Key;
                if (rb == null) continue;
                Vector3 target = platformTransform.TransformPoint(kv.Value);
                rb.MovePosition(target);
            }

            yield return null;
        }

        // Restore previous attachment override if we set it and it wasn't replaced by zone logic
        if (motor != null && platformRb != null && motor.AttachedRigidbodyOverride == platformRb)
        {
            motor.AttachedRigidbodyOverride = prevOverride;
        }
        // This coroutine only maintains relative alignment; the restoration gate is released by the caller
    }
    


    // Additional verification for extreme speed platforms (300+ m/s)
    private IEnumerator VerifyPlayerStabilityAtExtremeSpeed(KinematicPlatformPersistence platformPersist, KinematicPlatform platform, float verificationTime)
    {
        if (platformPersist == null || platform == null || playerController == null || playerController.Motor == null)
            yield break;

        var motor = playerController.Motor;
        var platformRb = platformPersist.GetComponent<Rigidbody>();
        
        float timeElapsed = 0f;
        Vector3 lastPlayerPos = playerTransform.position;
        Vector3 lastPlatformPos = platformPersist.transform.position;
        
        if (verboseLogging)
        {
            Log($"[PlayerProgression] Verifying player stability at extreme speed ({platform.MoveSpeed} m/s) for {verificationTime}s");
        }

        while (timeElapsed < verificationTime)
        {
            yield return new WaitForFixedUpdate();
            timeElapsed += Time.fixedDeltaTime;

            if (motor == null || platformRb == null) break;

            // Verify player is still attached to platform
            if (motor.AttachedRigidbodyOverride != platformRb)
            {
                motor.AttachedRigidbodyOverride = platformRb;
                if (verboseLogging)
                {
                    Log($"[PlayerProgression] Re-attached player to platform during extreme speed verification");
                }
            }

            // Verify player is maintaining relative position to platform
            Vector3 currentPlayerPos = playerTransform.position;
            Vector3 currentPlatformPos = platformPersist.transform.position;
            Vector3 platformDelta = currentPlatformPos - lastPlatformPos;
            Vector3 expectedPlayerPos = lastPlayerPos + platformDelta;
            float drift = Vector3.Distance(currentPlayerPos, expectedPlayerPos);

            if (drift > 0.5f) // Allow small drift tolerance
            {
                // Player is drifting, force correct position
                Vector3 localOffset = progressionData.playerLocalOffsetOnPlatform;
                Vector3 correctedPos = platformPersist.transform.TransformPoint(localOffset);
                motor.SetPosition(correctedPos, true);
                motor.BaseVelocity = Vector3.zero;
                
                if (verboseLogging)
                {
                    Log($"[PlayerProgression] Corrected player drift of {drift:F2}m during extreme speed verification");
                }
            }

            lastPlayerPos = currentPlayerPos;
            lastPlatformPos = currentPlatformPos;
        }

        if (verboseLogging)
        {
            Log($"[PlayerProgression] Extreme speed stability verification complete");
        }
    }
    
    // Coroutine to restore player velocity after a frame delay
    private float CalculateSettleTime(float platformSpeed)
    {
        float baseTime = 0.75f; // Increased base time for stability
        float speedFactor = platformSpeed / 100f;
        // More aggressive scaling for extreme speeds
        float dynamicTime = baseTime + speedFactor * 0.75f;
        // Cap at reasonable maximum but allow longer for extreme speeds
        return Mathf.Max(baseTime, Mathf.Min(dynamicTime, 5.0f));
    }

    private IEnumerator RestorePlayerVelocityDelayed(Vector3 velocity, bool isGrounded, bool wasFalling, float fallStartHeight, bool savedAirborne)
    {
        // Wait a frame to ensure the character controller is properly initialized
        yield return null;
        
        if (playerController != null && playerController.Motor != null)
        {
            // Apply the velocity to the character motor
            playerController.Motor.BaseVelocity = velocity;
            
            // If the player was airborne and had significant downward velocity, make sure they're not grounded
            if ((!isGrounded || savedAirborne) && velocity.y < -2f)
            {
                // Force the character to be considered airborne
                playerController.Motor.ForceUnground(0.1f);
            }

            // If we were falling before saving, re-arm fall tracking so landing will apply damage
            if (wasFalling)
            {
                var fallDamageSystem = playerTransform != null ? playerTransform.GetComponent<FallDamageSystem>() : null;
                if (fallDamageSystem != null)
                {
                    if (fallStartHeight > 0f)
                        fallDamageSystem.StartFallTrackingFromHeight(fallStartHeight);
                    else
                        fallDamageSystem.StartFallTracking();
                }
            }
            
            if (verboseLogging) {
                Log($"[PlayerProgression] Applied velocity: {velocity} to player (grounded: {isGrounded}, savedAirborne: {savedAirborne}, wasFalling: {wasFalling}, fallStartY: {fallStartHeight})");
            }
        }

        // Extra safety: keep enforcing for a few frames in case other systems reset velocity late
        for (int i = 0; i < 8; i++)
        {
            yield return null;
            if (playerController == null || playerController.Motor == null) break;

            var motor = playerController.Motor;
            var current = motor.BaseVelocity;

            // Stop enforcing if we became stably grounded (landing succeeded)
            if (motor.GroundingStatus.IsStableOnGround)
            {
                break;
            }

            // Only re-apply if our intended magnitude is significantly higher than current
            if (current.sqrMagnitude < velocity.sqrMagnitude * 0.49f) // 0.7x threshold
            {
                motor.BaseVelocity = velocity;
                if ((!isGrounded || savedAirborne) && velocity.y < -2f)
                {
                    motor.ForceUnground(0.05f);
                }
                if (verboseLogging)
                {
                    Log($"[PlayerProgression] Re-applied velocity during post-load enforcement (frame {i+1}): {velocity}");
                }
            }
        }
    }

    // Finish restoration after a specified number of frames
    private IEnumerator FinishRestorationAfterFrames(int frames)
    {
        for (int i = 0; i < frames; i++)
        {
            yield return null;
        }
        MarkRestorationComplete();
    }

    private void MarkRestorationComplete()
    {
        if (IsRestoring)
        {
            IsRestoring = false;
            Debug.Log("[PlayerProgression] Restoration complete - releasing mover/platform gating");
        }
        lastRestorationTime = Time.time;
        
        // Ensure all platforms are released from hold state
        foreach (var platformKvp in activePlatforms)
        {
            if (platformKvp.Value != null && platformKvp.Value.Platform != null)
            {
                platformKvp.Value.Platform.SetHoldForPlayerRestore(false);
            }
        }
    }

    private void PausePlatformsForItemRestore()
    {
        try
        {
            _platformsPausedForItemRestore.Clear();
            var platforms = UnityEngine.Object.FindObjectsByType<KinematicPlatform>(FindObjectsInactive.Include, FindObjectsSortMode.None);
            for (int i = 0; i < platforms.Length; i++)
            {
                var p = platforms[i];
                if (p == null) continue;
                p.SetSleepMode(true);
                _platformsPausedForItemRestore.Add(p);
            }
            if (verboseLogging && _platformsPausedForItemRestore.Count > 0)
            {
                Log($"[PlayerProgression] Paused {_platformsPausedForItemRestore.Count} platforms for item restoration");
            }
        }
        catch (Exception e)
        {
            LogWarning($"[PlayerProgression] Failed to pause platforms for item restore: {e.Message}");
        }
    }

    private void ResumePlatformsAfterItemRestore()
    {
        try
        {
            if (_platformsPausedForItemRestore != null && _platformsPausedForItemRestore.Count > 0)
            {
                for (int i = 0; i < _platformsPausedForItemRestore.Count; i++)
                {
                    var p = _platformsPausedForItemRestore[i];
                    if (p != null) p.SetSleepMode(false);
                }
                if (verboseLogging)
                {
                    Log($"[PlayerProgression] Resumed {_platformsPausedForItemRestore.Count} platforms after item restoration");
                }
                _platformsPausedForItemRestore.Clear();
            }
        }
        catch (Exception e)
        {
            LogWarning($"[PlayerProgression] Failed to resume platforms after item restore: {e.Message}");
        }
    }

    private IEnumerator ReleasePlatformHoldAfterTimeout(KinematicPlatform platform, float seconds)
    {
        if (platform == null) yield break;
        yield return new WaitForSeconds(seconds);
        platform.SetHoldForPlayerRestore(false);
    }

    // Safety coroutine to release platform holds in case restore doesn't run
    // Removed global safety release; release is driven by VerifyPlayerPlacementAndRelease

    private IEnumerator WaitUntilPassengersInZones(KinematicPlatformPersistence platformPersist, float timeoutSeconds, int requiredConsecutiveFrames)
    {
        if (platformPersist == null) yield break;

        var zones = platformPersist.GetComponentsInChildren<KinematicPlatformPassengerZone>(true);
        if (zones == null || zones.Length == 0) yield break;

        var platformData = progressionData.kinematicPlatforms
            .FirstOrDefault(p => p.id == platformPersist.UniqueId);
        if (platformData == null || platformData.passengers.Count == 0) yield break;

        float endTime = Time.time + timeoutSeconds;
        int consecutive = 0;

        while (Time.time < endTime)
        {
            bool allOk = true;

            foreach (var passengerData in platformData.passengers)
            {
                if (passengerData.type == PassengerType.Player)
                {
                    if (playerController == null || playerController.Motor == null)
                    {
                        allOk = false; break;
                    }
                    Vector3 playerPos = playerController.Motor.transform.position;
                    bool inZone = false;
                    for (int i = 0; i < zones.Length; i++)
                    {
                        var col = zones[i] != null ? zones[i].GetComponent<Collider>() : null;
                        if (col != null && col.bounds.Contains(playerPos)) { inZone = true; break; }
                    }
                    if (!inZone) { allOk = false; break; }
                }
                // Items/others are optional for gating; skip to avoid blocking on missing objects
            }

            if (allOk)
            {
                consecutive++;
                if (consecutive >= requiredConsecutiveFrames) break;
            }
            else
            {
                consecutive = 0;
            }

            yield return null;
        }

        if (platformPersist.Platform != null)
        {
            platformPersist.Platform.SetHoldForPlayerRestore(false);
        }
    }
    
    // Coroutine to restore camera rotation after a frame delay
    private IEnumerator RestoreCameraRotationDelayed(float pitch, float yaw)
    {
        // Wait a frame to ensure the camera controller is properly initialized
        yield return null;
        
        if (_cachedFpsCamera == null)
        {
            _cachedFpsCamera = Camera.main != null ? Camera.main.GetComponent<FPSCharacterCamera>() : null;
        }
        var fpsCamera = _cachedFpsCamera;
        if (fpsCamera != null)
        {
            // Apply the camera rotation
            fpsCamera.SetInitialRotation(pitch, yaw);
            
            if (verboseLogging) {
                Log($"[PlayerProgression] Applied camera rotation: pitch={pitch}, yaw={yaw}");
            }
        }
        else
        {
            if (verboseLogging) {
                Log($"[PlayerProgression] Could not find FPSCharacterCamera to restore rotation");
            }
        }
    }

    private bool playerPositionRestored = false; // Track if position has been applied

    // Public helper to fetch last saved player transform (for other systems)
    public bool TryGetSavedPlayerTransform(out Vector3 position, out Quaternion rotation)
    {
        if (progressionData != null && progressionData.playerPositionTimestamp > 0)
        {
            position = progressionData.playerPosition;
            rotation = progressionData.playerRotation;
            return true;
        }
        position = Vector3.zero;
        rotation = Quaternion.identity;
        return false;
    }
    
    // Public helper to fetch last saved camera rotation (for FPS camera)
    public bool TryGetSavedCameraRotation(out float pitch, out float yaw)
    {
        if (progressionData != null && progressionData.playerPositionTimestamp > 0)
        {
            pitch = progressionData.cameraRotationX;
            yaw = progressionData.cameraRotationY;
            return true;
        }
        
        // Fallback to PlayerPrefs if progression data isn't available
        if (PlayerPrefs.HasKey("camera_pitch") && PlayerPrefs.HasKey("camera_yaw"))
        {
            pitch = PlayerPrefs.GetFloat("camera_pitch");
            yaw = PlayerPrefs.GetFloat("camera_yaw");
            if (verboseLogging) {
                Log($"[PlayerProgression] Using camera rotation from PlayerPrefs: pitch={pitch}, yaw={yaw}");
            }
            return true;
        }
        
        pitch = 0f;
        yaw = 0f;
        return false;
    }
    
    // Public helper to get the saved scene name
    public string GetSavedSceneName()
    {
        return progressionData?.playerSceneName ?? "";
    }
    
    // Public method to save camera rotation explicitly (for scene transitions)
    public void SaveCameraRotation(float pitch, float yaw)
    {
        if (progressionData == null) return;
        
        progressionData.cameraRotationX = pitch;
        progressionData.cameraRotationY = yaw;
        
        // Also save to PlayerPrefs for immediate access
        PlayerPrefs.SetFloat("camera_pitch", pitch);
        PlayerPrefs.SetFloat("camera_yaw", yaw);
        PlayerPrefs.Save();
        
        if (verboseLogging) {
            Log($"[PlayerProgression] Saved camera rotation: pitch={pitch}, yaw={yaw}");
        }
    }
 }