{"totalVariantsIn": 19330, "totalVariantsOut": 5106, "shaders": [{"inputVariants": 4, "outputVariants": 0, "name": "StageSetupSegment", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 3.7759}, {"inputVariants": 2, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "DepthOfFieldCoC", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0181}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: KMainPhysical", "stripTimeMs": 0.018600000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0175}]}]}, {"inputVariants": 720, "outputVariants": 40, "name": "WaterLighting", "pipelines": [{"inputVariants": 720, "outputVariants": 40, "pipeline": "", "variants": [{"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.0767}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.0534}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.0509}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.0504}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.054700000000000006}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.054700000000000006}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.0538}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.0551}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.0567}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.063}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClearIndirect", "stripTimeMs": 0.0543}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterClassifyTiles", "stripTimeMs": 0.0524}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: PrepareSSRIndirect", "stripTimeMs": 0.0494}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant0", "stripTimeMs": 0.051300000000000005}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant1", "stripTimeMs": 0.0528}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant2", "stripTimeMs": 0.050300000000000004}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant3", "stripTimeMs": 0.0489}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterDeferredLighting_Variant4", "stripTimeMs": 0.0506}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogIndirect", "stripTimeMs": 0.051500000000000004}, {"inputVariants": 36, "outputVariants": 2, "variantName": "Kernel: WaterFogTransmittanceIndirect", "stripTimeMs": 0.0507}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "LensFlareMergeOcclusionDataDriven", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.019700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCS", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "OccluderDepthPyramidKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0351}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>D<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0175}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ProbeVolumeUploadData", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.06910000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: UploadData", "stripTimeMs": 0.0477}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BakeCloudTexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0274}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BakeCloudTexture", "stripTimeMs": 0.0103}]}]}, {"inputVariants": 13392, "outputVariants": 248, "name": "Deferred", "pipelines": [{"inputVariants": 13392, "outputVariants": 248, "pipeline": "", "variants": [{"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 0.23600000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.22060000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 0.2495}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 0.22210000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 0.2406}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 0.2144}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 0.2288}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 0.2359}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 0.22030000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 0.2806}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 0.299}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.3629}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 0.2121}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 0.2278}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 0.22360000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 0.2139}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 0.303}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 0.2059}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 0.3088}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 0.2922}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 0.2896}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.2849}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.2903}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.2844}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.28900000000000003}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.2884}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.2896}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.3008}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.2893}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.29660000000000003}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.29500000000000004}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl", "stripTimeMs": 0.2962}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Direct_Fptl_DebugDisplay", "stripTimeMs": 0.299}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant0", "stripTimeMs": 0.2914}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant1", "stripTimeMs": 0.2878}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant2", "stripTimeMs": 0.3012}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant3", "stripTimeMs": 0.2084}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant4", "stripTimeMs": 0.268}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant5", "stripTimeMs": 0.2106}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant6", "stripTimeMs": 0.23190000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant7", "stripTimeMs": 0.20800000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant8", "stripTimeMs": 0.20420000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant9", "stripTimeMs": 0.2056}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant10", "stripTimeMs": 0.20170000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant11", "stripTimeMs": 0.22080000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant12", "stripTimeMs": 0.2043}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant13", "stripTimeMs": 0.2172}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant14", "stripTimeMs": 0.21780000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant15", "stripTimeMs": 0.2076}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant16", "stripTimeMs": 0.2167}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant17", "stripTimeMs": 0.20850000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant18", "stripTimeMs": 0.2001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant19", "stripTimeMs": 0.2094}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant20", "stripTimeMs": 0.2069}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant21", "stripTimeMs": 0.21000000000000002}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant22", "stripTimeMs": 0.2078}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant23", "stripTimeMs": 0.2087}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant24", "stripTimeMs": 0.223}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant25", "stripTimeMs": 0.22290000000000001}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant26", "stripTimeMs": 0.2228}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant27", "stripTimeMs": 0.26230000000000003}, {"inputVariants": 216, "outputVariants": 4, "variantName": "Kernel: Deferred_Indirect_Fptl_Variant28", "stripTimeMs": 0.22160000000000002}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "lightlistbuild-clustered", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_NoDepthRT_SrcBigTile", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_Oblique", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_Oblique", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_SrcBigTile_Oblique", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileLightListGen_DepthRT_MSAA_SrcBigTile_Oblique", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "WaterFoam", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReprojectFoam", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AttenuateFoam", "stripTimeMs": 0.0039000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PaniniProjection", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.047400000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0171}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.0061}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: K<PERSON><PERSON><PERSON>mple", "stripTimeMs": 0.005}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "FXAA", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0349}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: FXAA", "stripTimeMs": 0.0142}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "StageRasterBin", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0103}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: MainArgs", "stripTimeMs": 0.0012000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ApplyExposure", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.016300000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0085}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldCoCDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "lightlistbuild-clearatomic", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0177}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearAtomic", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0346}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0187}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "WaterLine", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearWaterLine", "stripTimeMs": 0.0053}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LineEvaluation1D", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoundsPropagation", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HistogramExposure", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0239}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.0105}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0091}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KHistogramReduce", "stripTimeMs": 0.011300000000000001}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ProbeVolumeSamplingDebugPositionNormal", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0105}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputePositionNormal", "stripTimeMs": 0.0017000000000000001}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "VFXCopyBuffer", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.012100000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyBuffer", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBuffer", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitDeadListBuffer", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBuffer", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXZeroInitBufferUint", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountUint", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchCopyCountKvp", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXInitBoundsBuffer", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXCopyStructBufferSelf", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BloomPrefilter", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.014400000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VolumetricMaterial", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeVolumetricMaterialRenderingParameters", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ScreenSpaceMultipleScattering", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0145}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScreenSpaceMultipleScattering", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "InstanceTransformUpdateKernels", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.023100000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0105}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.0071}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterInitTransformMain", "stripTimeMs": 0.008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateTransformMain", "stripTimeMs": 0.0067}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateMotionMain", "stripTimeMs": 0.0066}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ScatterUpdateProbesMain", "stripTimeMs": 0.006500000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.022600000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MotionBlurCS", "stripTimeMs": 0.0054}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOSpatialDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.025900000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: SpatialDenoise", "stripTimeMs": 0.0114}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldMipSafe", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0258}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0094}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GTAOTemporalDenoise", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0195}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TemporalDenoise", "stripTimeMs": 0.0079}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "WaterSimulation", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0165}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InitializePhillipsSpectrum", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateDispersion", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormals", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateNormalsJacobian", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PrepareCausticsGeometry", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceData", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EvaluateInstanceDataInfinite", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DownsampleVTFeedback", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainMSAA", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurGenTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.024900000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileGenPass", "stripTimeMs": 0.0112}]}]}, {"inputVariants": 64, "outputVariants": 64, "name": "StpSetup", "pipelines": [{"inputVariants": 64, "outputVariants": 64, "pipeline": "", "variants": [{"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.0824}, {"inputVariants": 32, "outputVariants": 32, "variantName": "Kernel: StpSetup", "stripTimeMs": 0.0673}]}]}, {"inputVariants": 48, "outputVariants": 24, "name": "LutBuilder3D", "pipelines": [{"inputVariants": 48, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.09620000000000001}, {"inputVariants": 24, "outputVariants": 12, "variantName": "Kernel: KBuild", "stripTimeMs": 0.07980000000000001}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpTaa", "stripTimeMs": 0.0352}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldPreCombineFar", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.029900000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainPreCombineFar", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 4, "outputVariants": 2, "name": "ContactShadows", "pipelines": [{"inputVariants": 4, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.0223}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: DeferredContactShadow", "stripTimeMs": 0.009000000000000001}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "BakeCloudShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: BakeCloudShadows", "stripTimeMs": 0.037200000000000004}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "DepthOfFieldGather", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0487}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.028200000000000003}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: K<PERSON>ain<PERSON>ear", "stripTimeMs": 0.0256}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0244}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldCoCReproject", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0253}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0078000000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "HairMultipleScatteringPreIntegration", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationForward", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAttenuationBackward", "stripTimeMs": 0.0071}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeAzimuthalScattering", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeLongitudinalScattering", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "BlitAndExpose", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.022000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KAccumMain", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "MomentShadows", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0218}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMomentShadows", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableHorizontal", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MomentSummedAreaTableVertical", "stripTimeMs": 0.008}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ColorPyramid", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0253}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.0077}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.007500000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KColorDownsample", "stripTimeMs": 0.0071}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "builddispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.0291}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BuildIndirect", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RandomDownsample", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0253}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Downsample", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHistogramImage", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KHistogramGen", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StagePrepare", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0091}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCounters", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearBins", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearClusters", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ComputeClusterRanges", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearCountersPer<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: UpdateOffsets", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferUploadKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0128}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainUploadScatterInstances", "stripTimeMs": 0.0049}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ContrastAdaptiveSharpen", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0207}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 2, "outputVariants": 1, "variantName": "Kernel: KInitialize", "stripTimeMs": 0.0058000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BloomUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0275}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0078000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "MotionBlurNeighborhoodTilePass", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0235}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TileNeighbourhood", "stripTimeMs": 0.0078000000000000005}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "lightlistbuild-bigtile", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.024}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BigTileLightListGen", "stripTimeMs": 0.010700000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ProbeVolumeUploadDataL2", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0187}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UploadDataL2", "stripTimeMs": 0.0031000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugWaveform", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0088}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0011}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformGather", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KWaveformClear", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthPyramid", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0117}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KDepthDownsample8DualUav", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "lightlistbuild", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 8, "outputVariants": 8, "variantName": "Kernel: TileLightListGen", "stripTimeMs": 0.0262}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "ResolveStencilBuffer", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.053000000000000005}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: Main", "stripTimeMs": 0.0367}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "PostSharpenPass", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0367}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: SharpenCS", "stripTimeMs": 0.0137}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "GTAO", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0323}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: GTAOMain", "stripTimeMs": 0.0176}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BilateralUpsample", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0213}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColorHalf", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpSampleColor", "stripTimeMs": 0.0051}]}]}, {"inputVariants": 42, "outputVariants": 42, "name": "GenSdfRayMap", "pipelines": [{"inputVariants": 42, "outputVariants": 42, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InBucketSum", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlockSums", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FinalSum", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToTextureNormalized", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyTextures", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: JFA", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DistanceTransform", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Copy<PERSON><PERSON><PERSON>", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateRayMapLocal", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPass6Rays", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignPassNeighbors", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ToBlockSumBuffer", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearTexturesAndBuffers", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateTrianglesUV", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConservativeRasterization", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ChooseDirectionTriangleOnly", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SurfaceClosing", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanX", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanY", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RayMapScanZ", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DepthOfFieldClearIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0229}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>lear", "stripTimeMs": 0.0057}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugVectorscope", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.010100000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0017000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeGather", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: KVectorscopeClear", "stripTimeMs": 0.0014}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "AmbientProbeConvolution", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.0117}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuse", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionVolumetric", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionDiffuseVolumetric", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AmbientProbeConvolutionClouds", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InScatteredRadiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0172}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "scrbound", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0182}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "TemporalFilter", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0151}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ValidateHistory", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingle", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationSingleArray", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColor", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TemporalAccumulationColorArray", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyHistory", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArray", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistorySingleArrayNoValidity", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlendHistoryColorArray", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OutputHistoryArray", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearLightLists", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.022500000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearList", "stripTimeMs": 0.006900000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCircleOfConfusion", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0212}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0095}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCPhysical", "stripTimeMs": 0.0066}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KMainCoCManual", "stripTimeMs": 0.0068000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFMinMaxDilate", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.016300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ComputeGgxIblSampleData", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.015600000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeGgxIblSampleData", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EyeCausticLUTGen", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.0165}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SampleC<PERSON>tic", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyToLUT", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "GPUPrefixSum", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0154}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromConst", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCalculateLevelDispatchArgsFromBuffer", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroup", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumOnGroupExclusive", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumNextInput", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParent", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainPrefixSumResolveParentExclusive", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "VFXPrefixSum", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXPrepareSingleInstance", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBatchSumCount_128", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXBuildPrefixSum_128", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DepthOfFieldKernel", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricBlurKernel", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KParametricFloodfillKernel", "stripTimeMs": 0.0026000000000000003}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "SkyLUTGenerator", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MultiScatteringLUT", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SkyViewLUT", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTCamera", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringLUTWorld", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AtmosphericScatteringBlur", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GTAOCopyHistory", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0198}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GTAODenoise_CopyHistory", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 2, "outputVariants": 0, "name": "ClearDebugBuffer", "pipelines": [{"inputVariants": 2, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: clearMain", "stripTimeMs": 0.0013000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Texture3DAtlas", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0103}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GenerateMipMap", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "NaNKiller", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "VFXFillIndirectArgs", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.016300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CSVFXIndirectArgs", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DepthOfFieldTileMax", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.031}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.010700000000000001}]}]}, {"inputVariants": 12, "outputVariants": 0, "name": "StageRasterFine", "pipelines": [{"inputVariants": 12, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0114}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0016}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0012000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: Main", "stripTimeMs": 0.0013000000000000002}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "UpdateStrips", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0115}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UpdateParticleStrip", "stripTimeMs": 0.0066}]}]}, {"inputVariants": 24, "outputVariants": 16, "name": "ScreenSpaceGlobalIllumination", "pipelines": [{"inputVariants": 24, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.028800000000000003}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.013300000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.0122}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.011300000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIllumination", "stripTimeMs": 0.015300000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: TraceGlobalIlluminationHalf", "stripTimeMs": 0.0131}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIllumination", "stripTimeMs": 0.010700000000000001}, {"inputVariants": 3, "outputVariants": 2, "variantName": "Kernel: ReprojectGlobalIlluminationHalf", "stripTimeMs": 0.0108}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Exposure", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KFixedExposure", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KManualCameraExposure", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KPrePass", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KReduction", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "WaterDeformation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0238}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0076}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterDeformation", "stripTimeMs": 0.0067}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: EvaluateDeformationSurfaceGradient", "stripTimeMs": 0.0092}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "GTAOBlurAndUpsample", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BlurUpsample", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralUpsampling", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BoxUpsampling", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur_FullRes", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "PlanarReflectionFiltering", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0194}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: FilterPlanarReflection", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DownScale", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthConversion", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "ProbeVolumeBlendStates", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: BlendScenarios", "stripTimeMs": 0.0105}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "GPUCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0212}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_8", "stripTimeMs": 0.0056}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KSampleCopy4_1_x_1", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "StpPreTaa", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.052700000000000004}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Kernel: StpPreTaa", "stripTimeMs": 0.0424}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "EncodeBC6H", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KEncodeFastCubemapMip", "stripTimeMs": 0.0045000000000000005}]}]}, {"inputVariants": 32, "outputVariants": 16, "name": "SubsurfaceScattering", "pipelines": [{"inputVariants": 32, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.0396}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.0219}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: SubsurfaceScattering", "stripTimeMs": 0.021500000000000002}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Kernel: PackDiffusionProfile", "stripTimeMs": 0.0233}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "OcclusionCullingDebug", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0219}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearOcclusionDebug", "stripTimeMs": 0.0088}]}]}, {"inputVariants": 384, "outputVariants": 256, "name": "VolumetricLighting", "pipelines": [{"inputVariants": 384, "outputVariants": 256, "pipeline": "", "variants": [{"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.5900000000000001}, {"inputVariants": 192, "outputVariants": 128, "variantName": "Kernel: VolumetricLighting", "stripTimeMs": 0.5701}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DiffuseDenoiser", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.0216}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeneratePointDistribution", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterSingle", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BilateralFilterColor", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>ingle", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherColor", "stripTimeMs": 0.003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFComputeSlowTiles", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0236}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeSlowTiles", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "BuildProbabilityTables", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeConditionalDensities", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ComputeMarginalRowDensities", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "FourierTransform", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0189}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_256", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_256", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_128", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_128", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RowPassTi_64", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ColPassTi_64", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumeVoxelization", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.024200000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: VolumeVoxelization", "stripTimeMs": 0.0071}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "WaterEvaluation", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.045200000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: FindVerticalDisplacements", "stripTimeMs": 0.0256}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "AlphaCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DoFCoCMinMax", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.021400000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoCMinMax", "stripTimeMs": 0.005}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldCombine", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.10540000000000001}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0877}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "VrsTexture", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.06860000000000001}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureCopy", "stripTimeMs": 0.0443}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: TextureReduce", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "MotionBlurMergeTilePass", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0181}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TileMerge", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ClearUIntTextureArray", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0164}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTexture", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearUIntTextureArray", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 160, "outputVariants": 160, "name": "ScreenSpaceReflections", "pipelines": [{"inputVariants": 160, "outputVariants": 160, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0166}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.0142}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.013300000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.014}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.013600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.0149}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0146}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0137}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.013600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0149}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsTracing", "stripTimeMs": 0.013800000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsReprojection", "stripTimeMs": 0.0129}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBoth", "stripTimeMs": 0.0129}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnly", "stripTimeMs": 0.0145}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnly", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBoth", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnly", "stripTimeMs": 0.0129}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnly", "stripTimeMs": 0.013800000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBoth", "stripTimeMs": 0.013600000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnly", "stripTimeMs": 0.0146}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnly", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionBothDebug", "stripTimeMs": 0.0135}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0128}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateNoWorldSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.012700000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionBothDebug", "stripTimeMs": 0.013300000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.0135}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateHardThresholdSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0143}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionBothDebug", "stripTimeMs": 0.0134}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionSourceOnlyDebug", "stripTimeMs": 0.013800000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: ScreenSpaceReflectionsAccumulateSmoothSpeedRejectionTargetOnlyDebug", "stripTimeMs": 0.0126}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "DepthOfFieldMip", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0223}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColor", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorAlpha", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainCoC", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopy", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KMainColorCopyAlpha", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "DoFCombine", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.0409}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: UpsampleFastTiles", "stripTimeMs": 0.016800000000000002}]}]}, {"inputVariants": 10, "outputVariants": 0, "name": "StageWorkQueue", "pipelines": [{"inputVariants": 10, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.010700000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuildArgs", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueBuild", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveBins", "stripTimeMs": 0.0013000000000000002}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: WorkQueueActiveClusters", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: BuildFineRasterArgs", "stripTimeMs": 0.0012000000000000001}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "DoFApertureShape", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.020800000000000003}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeShape<PERSON>uffer", "stripTimeMs": 0.0095}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "GPUSort", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0122}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.011000000000000001}, {"inputVariants": 5, "outputVariants": 5, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.011300000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "GenerateMaxZ", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.0205}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0071}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.0046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeMaxZ", "stripTimeMs": 0.0046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ComputeFinalMask", "stripTimeMs": 0.0047}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DilateMask", "stripTimeMs": 0.0046}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Sort", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.008700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0054}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort128", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort1024_128", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort2048_128", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicSort4096_128", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass", "stripTimeMs": 0.0049}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass4096_128", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BitonicPrePass2048_128", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "GroundIrradiancePrecomputation", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.017}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: main", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Accumulation", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0409}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.021}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "MotionBlurMotionVecPrep", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0402}, {"inputVariants": 6, "outputVariants": 6, "variantName": "Kernel: MotionVecPreppingCS", "stripTimeMs": 0.0211}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "VolumetricLightingFiltering", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.0223}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: FilterVolumetricLighting", "stripTimeMs": 0.005}]}]}, {"inputVariants": 8, "outputVariants": 4, "name": "EdgeAdaptiveSpatialUpsampling", "pipelines": [{"inputVariants": 8, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.027200000000000002}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0134}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "DebugHDRxyMapping", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.015000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: KCIExyGen", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 256, "outputVariants": 128, "name": "UberPost", "pipelines": [{"inputVariants": 256, "outputVariants": 128, "pipeline": "", "variants": [{"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.43360000000000004}, {"inputVariants": 128, "outputVariants": 64, "variantName": "Kernel: Uber", "stripTimeMs": 0.4203}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "DepthOfFieldPrefilter", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0844}, {"inputVariants": 24, "outputVariants": 24, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0558}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "DebugLightVolumes", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.01}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0014}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeGradient", "stripTimeMs": 0.0015}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: LightVolumeColors", "stripTimeMs": 0.0013000000000000002}]}]}, {"inputVariants": 72, "outputVariants": 72, "name": "InstanceOcclusionCullingKernels", "pipelines": [{"inputVariants": 72, "outputVariants": 72, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.0516}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.040600000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: ResetDrawArgs", "stripTimeMs": 0.040600000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CopyInstances", "stripTimeMs": 0.0419}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: CullInstances", "stripTimeMs": 0.0398}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceDataBufferCopyKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MainCopyInstances", "stripTimeMs": 0.006900000000000001}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ClearBuffer2D", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.0164}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearBuffer2DMain", "stripTimeMs": 0.0073}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "EVSMBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0132}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ConvertAndBlur", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Blur", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CopyMoments", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "materialflags", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: MaterialFlagsGen", "stripTimeMs": 0.0053}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "cleardispatchindirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.016300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClearDispatchIndirect", "stripTimeMs": 0.0035}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "InstanceWindDataUpdateKernels", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.016300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: WindDataCopyHistoryMain", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 20, "outputVariants": 0, "name": "StageShadingSetup", "pipelines": [{"inputVariants": 20, "outputVariants": 0, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0077}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0009000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0008}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingSampleVisibilityBuffer", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingCompactionBuffer", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearShadingAtlas", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ClearHistogram", "stripTimeMs": 0.0019}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CopyHistoryToShadingAtlas", "stripTimeMs": 0.0006000000000000001}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CreateCompactedShadingSamplesMapping", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: ReplicateShadedSamplesToShadingAtlas", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHistogram", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: CalculateHighestVisibleHistogramID", "stripTimeMs": 0.0007}, {"inputVariants": 1, "outputVariants": 0, "variantName": "Kernel: DiscardSamplesBasedOnHistogram", "stripTimeMs": 0.0007}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "ReferenceImpl.IndexingOpsA", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Tile", "stripTimeMs": 0.0173}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherElementsFast", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElementsFast", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElements", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Expand", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Slice", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Tile", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherElementsFast", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElementsFast", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScatterElements", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Expand", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Slice", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TensorToTexture", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureExact", "stripTimeMs": 0.0262}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureLinear", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureExact", "stripTimeMs": 0.0068000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TensorToTextureLinear", "stripTimeMs": 0.0064}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "Resize", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Floor", "stripTimeMs": 0.019100000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Ceil", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Linear_None", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Floor", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Ceil", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Linear_None", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Floor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Ceil", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Linear_None", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Floor", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Ceil", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Linear_None", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Floor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Nearest_Ceil", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample1D_Linear_None", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Floor", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Nearest_Ceil", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample2D_Linear_None", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Floor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Nearest_Ceil", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Upsample3D_Linear_None", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Floor", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Nearest_Ceil", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Resize1D_Linear_None", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "GemmT", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_T8x8_R4x4", "stripTimeMs": 0.0179}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_WT_T8x8_R4x4", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_WT_T8x8_R4x4", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_T8x8_R4x4", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_WT_T8x8_R4x4", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmT_XT_WT_T8x8_R4x4", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "ImageBased", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceDepthColumnRow", "stripTimeMs": 0.0178}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceColumnRowDepth", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SpaceToDepth", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceDepthColumnRow", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DepthToSpaceColumnRowDepth", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SpaceToDepth", "stripTimeMs": 0.0055000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.014700000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "ReduceIndices", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatFirst", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatFirst", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatLast", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatLast", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntFirst", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntFirst", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntLast", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntLast", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatFirst", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatFirst", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxFloatLast", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinFloatLast", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntFirst", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntFirst", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxIntLast", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMinIntLast", "stripTimeMs": 0.0024000000000000002}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "IndexingOps", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OneHot", "stripTimeMs": 0.016900000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherND", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SliceSet", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OneHot", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GatherND", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SliceSet", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "AxisActivations", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LogSoftmaxEnd", "stripTimeMs": 0.016}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SoftmaxEnd", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardmaxEnd", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LogSoftmaxEnd", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SoftmaxEnd", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardmaxEnd", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Normalization", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LayerNormalizationTail", "stripTimeMs": 0.0146}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RMSNormalizationTail", "stripTimeMs": 0.005200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BatchNormalization", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScaleBias", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LayerNormalizationTail", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RMSNormalizationTail", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BatchNormalization", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScaleBias", "stripTimeMs": 0.0025}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "Random", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomUniform", "stripTimeMs": 0.015700000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomNormal", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BernoulliFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>lliInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TopP", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomUniform", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RandomNormal", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BernoulliFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>lliInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: TopP", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "NMS", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCorners", "stripTimeMs": 0.0166}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCenter", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSSelect", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSCompact", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCorners", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSBitmaskCenter", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSSelect", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NMSCompact", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 10, "outputVariants": 10, "name": "ReferenceImpl.PadA", "pipelines": [{"inputVariants": 10, "outputVariants": 10, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadBorderND", "stripTimeMs": 0.0166}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadReflectND", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadSymmetricND", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadEdgeND", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadWrapND", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadBorderND", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadReflectND", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadSymmetricND", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadEdgeND", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: PadWrapND", "stripTimeMs": 0.0022}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "RNN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LSTMEnd", "stripTimeMs": 0.0164}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LSTMEnd", "stripTimeMs": 0.0057}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "CumSum", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardInclusive", "stripTimeMs": 0.0158}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseInclusive", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardExclusive", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseExclusive", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardInclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseInclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardExclusive", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseExclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardInclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseInclusive", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatForwardExclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumFloatReverseExclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardInclusive", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseInclusive", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntForwardExclusive", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CumSumIntReverseExclusive", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "ScatterOps", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 10, "outputVariants": 10, "variantName": "Kernel: ScatterND", "stripTimeMs": 0.0391}, {"inputVariants": 10, "outputVariants": 10, "variantName": "Kernel: ScatterND", "stripTimeMs": 0.022600000000000002}]}]}, {"inputVariants": 38, "outputVariants": 38, "name": "Compute.Shaders.ReductionUnrolled.gen", "pipelines": [{"inputVariants": 38, "outputVariants": 38, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxFloat", "stripTimeMs": 0.0171}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinFloat", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumFloat", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanSquareFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Float", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL2Float", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSqrtFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumExpFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdInt", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Int", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumFloat", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanSquareFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL2Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSqrtFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumExpFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceMinInt", "stripTimeMs": 0.0027}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceSumSquareInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceProdInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: UnrolledReduceL1Int", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 24, "outputVariants": 24, "name": "<PERSON><PERSON>", "pipelines": [{"inputVariants": 24, "outputVariants": 24, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T16x16_R4x4", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T16x16_R4x4", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T8x8_R4x4", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T8x8_R4x4", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T16x16_R4x4", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T8x8_R4x4", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T16x16_R4x4", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T8x8_R4x4", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_V_L1Cached64", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_V_L1Cached64", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_V_L1Cached64", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_V_L1Cached64", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T16x16_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T16x16_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_T8x8_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_T8x8_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T16x16_R4x4", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_T8x8_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T16x16_R4x4", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_T8x8_R4x4", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Dense_V_L1Cached64", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Gemm_V_L1Cached64", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GemmBatched_V_L1Cached64", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DenseBatched_V_L1Cached64", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ReferenceImpl.GenericA", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose", "stripTimeMs": 0.020200000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InstanceNormalizationTail", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose", "stripTimeMs": 0.0063}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: InstanceNormalizationTail", "stripTimeMs": 0.004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "BitonicSort", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortStep", "stripTimeMs": 0.024}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortKeyStep", "stripTimeMs": 0.0076}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortStep", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: BitonicSortKeyStep", "stripTimeMs": 0.007200000000000001}]}]}, {"inputVariants": 2304, "outputVariants": 2304, "name": "ConvGeneric", "pipelines": [{"inputVariants": 2304, "outputVariants": 2304, "pipeline": "", "variants": [{"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_Generic", "stripTimeMs": 0.3286}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_Generic", "stripTimeMs": 0.33290000000000003}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_Generic", "stripTimeMs": 0.3502}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_1x1_Generic", "stripTimeMs": 0.3271}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_1x1_Generic", "stripTimeMs": 0.312}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_1x1_Generic", "stripTimeMs": 0.3296}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_Generic", "stripTimeMs": 0.3269}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_Generic", "stripTimeMs": 0.33280000000000004}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_Generic", "stripTimeMs": 0.3279}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_1x1_Generic", "stripTimeMs": 0.3275}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_1x1_Generic", "stripTimeMs": 0.33230000000000004}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_1x1_Generic", "stripTimeMs": 0.3266}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_Generic", "stripTimeMs": 0.31930000000000003}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_Generic", "stripTimeMs": 0.3236}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_Generic", "stripTimeMs": 0.3214}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv3D_1x1_Generic", "stripTimeMs": 0.3239}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv2D_1x1_Generic", "stripTimeMs": 0.2313}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: Conv1D_1x1_Generic", "stripTimeMs": 0.2853}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_Generic", "stripTimeMs": 0.3003}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_Generic", "stripTimeMs": 0.2235}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_Generic", "stripTimeMs": 0.2016}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose3D_1x1_Generic", "stripTimeMs": 0.2242}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose2D_1x1_Generic", "stripTimeMs": 0.28450000000000003}, {"inputVariants": 96, "outputVariants": 96, "variantName": "Kernel: ConvTranspose1D_1x1_Generic", "stripTimeMs": 0.26130000000000003}]}]}, {"inputVariants": 132, "outputVariants": 132, "name": "Compute.Shaders.Broadcast.gen", "pipelines": [{"inputVariants": 132, "outputVariants": 132, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPRelu", "stripTimeMs": 0.020300000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPRelu", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePRelu", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatFloat", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatFloat", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatFloat", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivFloat", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMeanFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMeanFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMeanFloat", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModFloat", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddInt", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinInt", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxInt", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModInt", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPRelu", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPRelu", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePRelu", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowFloatInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowFloatInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowFloatInt", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivFloat", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivFloat", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMeanFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMeanFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMeanFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModFloat", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModFloat", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntFloat", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastPowIntInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastPowIntInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwisePowIntInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastAddInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastAddInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseAddInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastSubInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastSubInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseSubInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMulInt", "stripTimeMs": 0.007200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMulInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMulInt", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastDivInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastDivInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseDivInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMinInt", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMinInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMinInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastMaxInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastMaxInt", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseMaxInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastModInt", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastModInt", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseModInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarBroadcastFModInt", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: BroadcastFModInt", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ElementwiseFModInt", "stripTimeMs": 0.0036000000000000003}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "SortingOps", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: TopK", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: TopK", "stripTimeMs": 0.0097}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "CopyOps", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Split", "stripTimeMs": 0.0193}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0051}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopy", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopyStride", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemSet", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose2D", "stripTimeMs": 0.0061}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastHalfToFloat", "stripTimeMs": 0.006200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DequantizeUint8", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Split", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopy", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemCopyStride", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MemSet", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Transpose2D", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastHalfToFloat", "stripTimeMs": 0.0055000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: DequantizeUint8", "stripTimeMs": 0.0033}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextureToTensor", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorExact", "stripTimeMs": 0.0241}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorLinear", "stripTimeMs": 0.0091}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorExact", "stripTimeMs": 0.013000000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: TextureToTensorLinear", "stripTimeMs": 0.0079}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "DepthwiseConv", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DWinograd", "stripTimeMs": 0.0213}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KernelWinoExpand", "stripTimeMs": 0.0076}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DDirect", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DWinograd", "stripTimeMs": 0.007}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: KernelWinoExpand", "stripTimeMs": 0.0071}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: DepthwiseConv2DDirect", "stripTimeMs": 0.0073}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Compute.Shaders.ConvTranspose.gen", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose2D_KxK", "stripTimeMs": 0.026600000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose1D_KxK", "stripTimeMs": 0.0079}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose2D_KxK", "stripTimeMs": 0.0074}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: ConvTranspose1D_KxK", "stripTimeMs": 0.010100000000000001}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Pool", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePoolReduce", "stripTimeMs": 0.0199}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAveragePool", "stripTimeMs": 0.0058000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPoolReduce", "stripTimeMs": 0.0047}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalMaxPool", "stripTimeMs": 0.0044}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AverageVariancePoolReduce", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAverageVariancePool", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxReduce", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalArgMaxReduce", "stripTimeMs": 0.0064}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePoolReduce", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAveragePool", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPoolReduce", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalMaxPool", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AverageVariancePoolReduce", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalAverageVariancePool", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ArgMaxReduce", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalArgMaxReduce", "stripTimeMs": 0.0041}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "ReferenceImpl.Logical", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Where", "stripTimeMs": 0.0221}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Where", "stripTimeMs": 0.0044}]}]}, {"inputVariants": 110, "outputVariants": 110, "name": "Compute.Shaders.PointwiseUnary.gen", "pipelines": [{"inputVariants": 110, "outputVariants": 110, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LeakyRelu", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>sh", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Relu6", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0057}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeluFast", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSigmoid", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>nk", "stripTimeMs": 0.0043}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ThresholdedRelu", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softplus", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Floor", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Round", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Reciprocal", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Exp", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Log", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sqrt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acos", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acosh", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Cos", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sin", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softsign", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSwish", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsInt", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsFloat", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegFloat", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareInt", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareFloat", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsNaN", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastIntToFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastFloatToInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignFloat", "stripTimeMs": 0.0033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Not", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipInt", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadInt", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LeakyRelu", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>sh", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Relu6", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GeluFast", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSigmoid", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>nk", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ThresholdedRelu", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softplus", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Floor", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Round", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Reciprocal", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Exp", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Log", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sqrt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acos", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Acosh", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON><PERSON>", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Cos", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Sin", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON><PERSON>", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: <PERSON>", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Softsign", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: HardSwish", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AbsFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegInt", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: NegFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareInt", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SquareFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsNaN", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastIntToFloat", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: CastFloatToInt", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignFloat", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: SignInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: Not", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ClipInt", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadFloat", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ScalarMadInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeFloat", "stripTimeMs": 0.0041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: RangeInt", "stripTimeMs": 0.0037}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "ReferenceImpl.PoolA", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool2D", "stripTimeMs": 0.0196}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool2D", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool1D", "stripTimeMs": 0.0039000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool1D", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool2D", "stripTimeMs": 0.0034000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool2D", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: MaxPool1D", "stripTimeMs": 0.0061}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AveragePool1D", "stripTimeMs": 0.0034000000000000002}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "ReferenceImpl.Einsum", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumOne", "stripTimeMs": 0.0195}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumTwo", "stripTimeMs": 0.0048000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumOne", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EinsumTwo", "stripTimeMs": 0.004200000000000001}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "LogicalOps", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OrInt", "stripTimeMs": 0.0204}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AndInt", "stripTimeMs": 0.0046}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: XorInt", "stripTimeMs": 0.004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsInf", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: OrInt", "stripTimeMs": 0.0037}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: AndInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: XorInt", "stripTimeMs": 0.0036000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: IsInf", "stripTimeMs": 0.0047}]}]}, {"inputVariants": 16, "outputVariants": 16, "name": "Compute.Shaders.Conv.gen", "pipelines": [{"inputVariants": 16, "outputVariants": 16, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_KxK", "stripTimeMs": 0.028300000000000002}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_1x1", "stripTimeMs": 0.0118}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_KxK", "stripTimeMs": 0.008400000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_1x1", "stripTimeMs": 0.0079}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_KxK", "stripTimeMs": 0.0082}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv2D_1x1", "stripTimeMs": 0.008}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_KxK", "stripTimeMs": 0.0078000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Kernel: Conv1D_1x1", "stripTimeMs": 0.0083}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "RoiAlign", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: RoiAlign", "stripTimeMs": 0.0349}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Kernel: RoiAlign", "stripTimeMs": 0.012400000000000001}]}]}, {"inputVariants": 48, "outputVariants": 48, "name": "GridSample", "pipelines": [{"inputVariants": 48, "outputVariants": 48, "pipeline": "", "variants": [{"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample2D", "stripTimeMs": 0.0543}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample3D", "stripTimeMs": 0.028}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample2D", "stripTimeMs": 0.0262}, {"inputVariants": 12, "outputVariants": 12, "variantName": "Kernel: GridSample3D", "stripTimeMs": 0.0251}]}]}, {"inputVariants": 76, "outputVariants": 76, "name": "Compute.Shaders.Reduction.gen", "pipelines": [{"inputVariants": 76, "outputVariants": 76, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxFloat", "stripTimeMs": 0.018000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumFloat", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareFloat", "stripTimeMs": 0.0038}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanSquareFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdFloat", "stripTimeMs": 0.0031000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Float", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL2Float", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL2Float", "stripTimeMs": 0.004200000000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSqrtFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSqrtFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumExpFloat", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxInt", "stripTimeMs": 0.0028}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdInt", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Int", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Int", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxFloat", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinFloat", "stripTimeMs": 0.0035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanSquareFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMeanFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMeanFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Float", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL2Float", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL2Float", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSqrtFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSqrtFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumFloat", "stripTimeMs": 0.0021000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceLogSumExpFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceLogSumExpFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumExpFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumExpFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMaxInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceMinInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceSumSquareInt", "stripTimeMs": 0.003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceSumSquareInt", "stripTimeMs": 0.0029000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceProdInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceProdInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: ReduceL1Int", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GlobalReduceL1Int", "stripTimeMs": 0.0023}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "CompareOps", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterFloat", "stripTimeMs": 0.0177}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterInt", "stripTimeMs": 0.0032}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualFloat", "stripTimeMs": 0.0026000000000000003}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualInt", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualInt", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualFloat", "stripTimeMs": 0.0025}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualInt", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterFloat", "stripTimeMs": 0.0045000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterInt", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: GreaterOrEqualInt", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessFloat", "stripTimeMs": 0.0024000000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualFloat", "stripTimeMs": 0.0023}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: LessOrEqualInt", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualFloat", "stripTimeMs": 0.0022}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Kernel: EqualInt", "stripTimeMs": 0.0023}]}]}]}