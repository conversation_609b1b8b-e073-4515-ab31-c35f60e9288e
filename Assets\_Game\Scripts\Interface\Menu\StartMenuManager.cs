using UnityEngine;
using UnityEngine.UIElements;
using System.Collections.Generic;
using System.Linq;
using System.Collections;

public class StartMenuManager : BaseMenuManager
{
    // Store the original title text to restore it later
    private string originalTitle = "";

    // Loading state management
    private Button continueButton;
    private bool isLoading = false;
    private string originalContinueText = "Continue";
    private Coroutine pulseCoroutine;

    protected override void Awake()
    {
        base.Awake();
        ValidateRequiredComponents();
    }

    protected override void Start()
    {
        base.Start();

        // REMOVED: Shader.WarmupAllShaders() - causes GPU device removal crashes!
        // DO NOT USE shader warmup in builds - it's unstable on many drivers

        // Store the original title text
        if (titleLabel != null)
        {
            originalTitle = titleLabel.text;
        }

        SetupStartMenuButtons();

        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Show main menu buttons
        ShowStartMenu();

        // REMOVED: Settings already applied by BaseMenuManager.Start() - prevents spam
    }

    protected override void InitializeUIElements()
    {
        base.InitializeUIElements();

        // Set up menu container visibility
        if (menuContainer != null)
        {
            menuContainer.style.display = DisplayStyle.Flex;
        }
    }

    private void OnEnable()
    {
        // Ensure cursor visible when menu is enabled in single scene
        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Block player UI overlays
        var toolUI = FindFirstObjectByType<ToolSelectionManager>();
        if (toolUI != null)
        {
            toolUI.ForceHideUI();
        }

        // If this GameObject was disabled during gameplay, UIDocument's panel/root
        // gets recreated when re-enabled. Refresh references and rebind UI if needed.
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
        }

        var currentRoot = uiDocument != null ? uiDocument.rootVisualElement : null;
        if (currentRoot != null && currentRoot != root)
        {
            // Clear previous bindings tied to the old visual tree
            CleanupButtons();
            CleanupSettingsEvents();

            // Re-initialize UI hierarchy and settings, then rebuild menu buttons
            InitializeUIElements();
            InitializeSettings();
            SetupStartMenuButtons();
            ShowStartMenu();
        }

        // Safety: some Unity versions construct UIDocument root after OnEnable.
        // Recheck next frame and rebind if still needed.
        StartCoroutine(RebindIfNeededNextFrame());
    }

    private System.Collections.IEnumerator RebindIfNeededNextFrame()
    {
        yield return null;
        if (uiDocument == null)
        {
            uiDocument = GetComponent<UIDocument>();
        }
        var currentRoot = uiDocument != null ? uiDocument.rootVisualElement : null;
        if (currentRoot != null && currentRoot != root)
        {
            CleanupButtons();
            CleanupSettingsEvents();
            InitializeUIElements();
            InitializeSettings();
            SetupStartMenuButtons();
            ShowStartMenu();
        }
    }

    private void SetupStartMenuButtons()
    {
        // Clear existing menu buttons but preserve settings buttons
        var existingSettingsButtons = settingsButtons.ToList();
        menuButtons.Clear();
        settingsButtons = existingSettingsButtons;

        // Setup main menu buttons using our local button setup helper
        SetupButton("continue-button", ContinueGame);
        SetupButton("settings-button", OpenSettings);
        SetupButton("credits-button", ShowCredits);
        SetupButton("quit-game-button", QuitGame);

        // Capture continue button reference for loading state
        continueButton = root.Q<Button>("continue-button");
        if (continueButton != null)
        {
            originalContinueText = continueButton.text;
        }

        // Ensure buttons are visible
        ShowStartMenu();
    }

    protected override void ShowStartMenu()
    {
        base.ShowStartMenu();
        
        // Restore the original title when returning from settings
        if (titleLabel != null)
        {
            titleLabel.text = originalTitle;
        }
    }

    protected override void ShowSettingsMenu()
    {
        base.ShowSettingsMenu();
        if (titleLabel != null)
        {
            titleLabel.text = "Settings";
        }
    }

    private void ValidateRequiredComponents()
    {
        if (GameFlowManager.Instance == null)
        {
            Debug.LogError("GameFlowManager singleton instance is not available in StartMenuManager");
        }
    }

    private void ContinueGame()
    {
        if (isLoading)
        {
            return; // Prevent multiple clicks during loading
        }

        if (GameFlowManager.Instance == null)
        {
            Debug.LogError("Cannot continue game: GameFlowManager is null");
            return;
        }

        // Start loading process
        StartCoroutine(LoadGameplayCoroutine());
    }

    private IEnumerator LoadGameplayCoroutine()
    {
        SceneTransitionLogger.LogTransitionStep("MENU_LOAD_START", "StartMenuManager LoadGameplayCoroutine started");
        isLoading = true;

        // Start loading animation
        SceneTransitionLogger.LogTransitionStep("LOADING_ANIMATION_START", "Starting loading animation");
        StartLoadingAnimation();

        // Initialize persistence manager while staying in menu
        SceneTransitionLogger.LogTransitionStep("PERSISTENCE_INIT_START", "Starting PersistenceManager initialization");
        if (PersistenceManager.Instance != null)
        {
            Debug.Log("[StartMenu] Starting deferred initialization...");
            Debug.Log($"[StartMenu] Initial IsInitializationComplete: {PersistenceManager.Instance.IsInitializationComplete}");

            // Start the initialization
            SceneTransitionLogger.LogPotentialFreeze("About to call PersistenceManager.InitializeForGameplay");
            PersistenceManager.Instance.InitializeForGameplay();
            SceneTransitionLogger.LogTransitionStep("PERSISTENCE_INIT_CALLED", "PersistenceManager.InitializeForGameplay called");

            // Wait for actual completion instead of arbitrary time
            Debug.Log("[StartMenu] Waiting for initialization to complete...");
            int waitFrames = 0;
            while (PersistenceManager.Instance != null && !PersistenceManager.Instance.IsInitializationComplete && waitFrames < 300)
            {
                waitFrames++;
                if (waitFrames % 60 == 0) // Log every second
                {
                    SceneTransitionLogger.LogTransitionStep("PERSISTENCE_INIT_WAITING", $"Waiting for persistence init completion (frame {waitFrames})");
                }
                if (waitFrames > 180) // More than 3 seconds
                {
                    SceneTransitionLogger.LogPotentialFreeze($"PersistenceManager initialization taking too long - {waitFrames} frames");
                }
                yield return null;
            }

            SceneTransitionLogger.LogTransitionStep("PERSISTENCE_INIT_COMPLETE", $"PersistenceManager initialization completed after {waitFrames} frames");
            Debug.Log($"[StartMenu] Initialization complete after {waitFrames} frames!");
        }
        else
        {
            SceneTransitionLogger.LogTransitionStep("PERSISTENCE_INIT_ERROR", "PersistenceManager.Instance is null!");
            Debug.LogError("[StartMenu] PersistenceManager.Instance is null!");
        }

        // Stop loading animation immediately - no artificial delays
        SceneTransitionLogger.LogTransitionStep("LOADING_ANIMATION_STOP", "Stopping loading animation");
        Debug.Log("[StartMenu] Stopping loading animation...");
        StopLoadingAnimation();

        Debug.Log("[StartMenu] Transitioning to gameplay...");
        SceneTransitionLogger.LogTransitionStep("GAMEPLAY_TRANSITION_START", "Starting transition to gameplay");

        // Now transition to gameplay
        SceneTransitionLogger.LogPotentialFreeze("About to call GameFlowManager.EnterGameplay");
        GameFlowManager.Instance.EnterGameplay();
        SceneTransitionLogger.LogTransitionStep("GAMEPLAY_TRANSITION_COMPLETE", "GameFlowManager.EnterGameplay completed");

        isLoading = false;
        SceneTransitionLogger.LogTransitionStep("MENU_LOAD_COMPLETE", "StartMenuManager loading process complete");
        Debug.Log("[StartMenu] Loading process complete!");
    }

    private void StartLoadingAnimation()
    {
        if (continueButton != null)
        {
            continueButton.text = "Loading...";
            continueButton.AddToClassList("loading-pulse");
            continueButton.SetEnabled(false);

            // Start pulsing animation
            if (pulseCoroutine != null)
            {
                StopCoroutine(pulseCoroutine);
            }
            pulseCoroutine = StartCoroutine(PulseAnimation());
        }
    }

    private void StopLoadingAnimation()
    {
        Debug.Log("[StartMenu] StopLoadingAnimation called");
        if (continueButton != null)
        {
            Debug.Log("[StartMenu] Restoring button text and state");
            continueButton.text = originalContinueText;
            continueButton.RemoveFromClassList("loading-pulse");
            continueButton.SetEnabled(true);

            // Stop pulsing animation
            if (pulseCoroutine != null)
            {
                Debug.Log("[StartMenu] Stopping pulse coroutine");
                StopCoroutine(pulseCoroutine);
                pulseCoroutine = null;
            }
        }
        else
        {
            Debug.LogError("[StartMenu] continueButton is null in StopLoadingAnimation!");
        }
        Debug.Log("[StartMenu] StopLoadingAnimation complete");
    }

    private IEnumerator PulseAnimation()
    {
        while (isLoading && continueButton != null)
        {
            // Pulse the text between "Loading..." and "Loading"
            continueButton.text = "Loading...";
            yield return new WaitForSeconds(0.5f);

            if (isLoading && continueButton != null)
            {
                continueButton.text = "Loading";
                yield return new WaitForSeconds(0.5f);
            }
        }
    }

    private void ShowCredits()
    {
        Debug.Log("Credits not yet implemented");
    }

    private void QuitGame()
    {
        if (GameFlowManager.Instance != null)
        {
            GameFlowManager.Instance.QuitGame();
        }
        else
        {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
            Application.Quit();
#endif
        }
    }



    protected override void OnDestroy()
    {
        // Clean up loading animation
        if (pulseCoroutine != null)
        {
            StopCoroutine(pulseCoroutine);
            pulseCoroutine = null;
        }

        base.OnDestroy();
    }
}