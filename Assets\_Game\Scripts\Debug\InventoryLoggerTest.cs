using UnityEngine;
using System.IO;

/// <summary>
/// Test component to verify inventory logging is working
/// Add this to any GameObject in the scene to test the inventory logging system
/// </summary>
public class InventoryLoggerTest : MonoBehaviour
{
    [Header("Inventory Logger Test")]
    [SerializeField] private bool runTestOnStart = false;
    [SerializeField] private KeyCode testKey = KeyCode.F9;
    
    private EquipmentManager equipmentManager;
    
    private void Start()
    {
        equipmentManager = FindObjectOfType<EquipmentManager>();
        
        if (runTestOnStart)
        {
            TestInventoryLogging();
        }
    }
    
    private void Update()
    {
        if (Input.GetKeyDown(testKey))
        {
            TestInventoryLogging();
        }
    }
    
    [ContextMenu("Test Inventory Logging")]
    public void TestInventoryLogging()
    {
        if (equipmentManager == null)
        {
            equipmentManager = FindObjectOfType<EquipmentManager>();
        }
        
        if (equipmentManager != null)
        {
            Debug.Log("[InventoryLoggerTest] Triggering manual inventory state log...");
            equipmentManager.ForceLogInventoryState("TEST_MANUAL_TRIGGER");
            
            // Also check if log file exists and show its location
            string logPath = Path.Combine(Application.persistentDataPath, "inventory_state.log");
            if (File.Exists(logPath))
            {
                Debug.Log($"[InventoryLoggerTest] Log file exists at: {logPath}");
                
                // Show last few lines of the log
                try
                {
                    string[] lines = File.ReadAllLines(logPath);
                    int startIndex = Mathf.Max(0, lines.Length - 5);
                    Debug.Log("[InventoryLoggerTest] Last 5 log entries:");
                    for (int i = startIndex; i < lines.Length; i++)
                    {
                        Debug.Log($"  {lines[i]}");
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"[InventoryLoggerTest] Error reading log file: {e.Message}");
                }
            }
            else
            {
                Debug.LogWarning($"[InventoryLoggerTest] Log file not found at: {logPath}");
            }
        }
        else
        {
            Debug.LogError("[InventoryLoggerTest] EquipmentManager not found!");
        }
    }
    
    [ContextMenu("Open Log File Location")]
    public void OpenLogFileLocation()
    {
        string logPath = Path.Combine(Application.persistentDataPath, "inventory_state.log");
        string directory = Path.GetDirectoryName(logPath);
        
        Debug.Log($"[InventoryLoggerTest] Log directory: {directory}");
        
        // Try to open the directory in file explorer
        try
        {
            System.Diagnostics.Process.Start(directory);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[InventoryLoggerTest] Could not open directory: {e.Message}");
        }
    }
    
    [ContextMenu("Clear Log File")]
    public void ClearLogFile()
    {
        string logPath = Path.Combine(Application.persistentDataPath, "inventory_state.log");
        
        try
        {
            if (File.Exists(logPath))
            {
                File.Delete(logPath);
                Debug.Log("[InventoryLoggerTest] Log file cleared.");
            }
            else
            {
                Debug.Log("[InventoryLoggerTest] Log file doesn't exist.");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[InventoryLoggerTest] Error clearing log file: {e.Message}");
        }
    }
}
