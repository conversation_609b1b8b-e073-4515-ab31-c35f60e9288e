# Scene Transition Logging System

## Overview
This system comprehensively monitors scene transitions to identify the cause of hard freezes when transitioning from the Boot scene to the Main scene. It logs every step of the loading process with precise timing, system monitoring, and freeze detection.

## The Problem
You're experiencing a 1/3 chance of hard freezes that bring down your entire PC when transitioning from the Start scene to the Main scene. This suggests:
- Infinite loops in initialization code
- Massive memory allocations
- Deadlocks in threading
- GPU driver issues
- Resource loading bottlenecks

## Log File Location
The log file is saved at: `%USERPROFILE%/AppData/LocalLow/[CompanyName]/[GameName]/scene_transition.log`

## What Gets Logged

### Boot Process
- BootLoader initialization steps
- Scene validation and loading
- Async operation progress
- Scene activation timing

### Menu Transitions
- StartMenuManager loading process
- PersistenceManager initialization (the heavy operation)
- GameFlowManager state changes

### System Monitoring
- Memory usage tracking
- CPU usage monitoring (background thread)
- Frame time analysis
- Thread count monitoring

### Critical Points
The system specifically monitors these freeze-prone operations:
1. `SceneManager.LoadSceneAsync()` call
2. `PersistenceManager.InitializeForGameplay()` 
3. Scene activation (`op.allowSceneActivation = true`)
4. Heavy initialization routines

## Log Entry Format
```
[2024-01-15 14:30:25.123] [T1] STEP_BOOTLOADER_AWAKE | BootLoader Awake started [+0ms]
[2024-01-15 14:30:25.456] [T1] POTENTIAL_FREEZE | About to call SceneManager.LoadSceneAsync | Memory: 1024MB | Threads: 12
```

Each entry contains:
- **Timestamp**: Precise time (milliseconds)
- **Thread ID**: Which thread logged the event
- **Event Type**: What operation was performed
- **Details**: Context and system state
- **Elapsed Time**: Time since transition started
- **Stack Trace**: For critical events

## Key Events to Watch For

### Normal Flow
```
BOOTLOADER_AWAKE → ASYNC_LOAD_START → LOAD_PROGRESS_COMPLETE → SCENE_ACTIVATION_ENABLED → SCENE_ACTIVATION_COMPLETE
```

### Freeze Indicators
- `POTENTIAL_FREEZE`: System detected a risky operation
- `HIGH_CPU_USAGE`: CPU usage above 90%
- `FRAME_HITCH`: Frame took longer than 100ms
- Long gaps between log entries (indicates freeze)

## Using the System

### Automatic Logging
The system starts automatically when the game boots and logs everything.

### Manual Testing
1. Add `SceneTransitionLoggerTest` component to any GameObject
2. Press F10 to test logging
3. Press F11 to clear log file
4. Use context menu options for more tests

### Analyzing Freezes
When a freeze occurs, look for:

1. **Last logged event** before the freeze
2. **Time gaps** in the log (indicates where freeze happened)
3. **Memory/CPU spikes** before the freeze
4. **Stack traces** showing what code was executing

## Common Freeze Patterns

### Pattern 1: Async Loading Freeze
```
ASYNC_LOAD_START | Starting async load
[LONG GAP - FREEZE OCCURRED HERE]
```
**Cause**: Unity's scene loading got stuck

### Pattern 2: Persistence Initialization Freeze
```
PERSISTENCE_INIT_CALLED | PersistenceManager.InitializeForGameplay called
[LONG GAP - FREEZE OCCURRED HERE]
```
**Cause**: Heavy initialization in PersistenceManager

### Pattern 3: Scene Activation Freeze
```
SCENE_ACTIVATION_ENABLED | Scene activation enabled
[LONG GAP - FREEZE OCCURRED HERE]
```
**Cause**: Scene activation triggering heavy operations

## Performance Impact
- Minimal during normal operation
- Background thread monitoring for CPU/memory
- File I/O on background threads
- Can be disabled if needed

## Troubleshooting Steps

1. **Run the game** and try to reproduce the freeze
2. **Check the log file** immediately after the freeze
3. **Identify the last logged event** before the freeze
4. **Look for patterns** - does it always freeze at the same step?
5. **Check system resources** - memory/CPU spikes in the log

## Example Investigation

If you see this pattern:
```
[14:30:25.123] PERSISTENCE_INIT_CALLED | PersistenceManager.InitializeForGameplay called
[14:30:25.456] PERF_CHECK | Memory: 2048MB | FPS: 60.0 | Threads: 15
[14:30:27.789] HIGH_CPU_USAGE | CPU: 95.2% | Memory: 3072MB | Threads: 15
[No more entries - freeze occurred]
```

This tells you:
- The freeze happened during PersistenceManager initialization
- Memory usage spiked from 2GB to 3GB
- CPU usage hit 95%
- The freeze occurred around 14:30:27

## Next Steps
Once you identify where the freeze occurs, you can:
1. Add more detailed logging to that specific area
2. Break down heavy operations into smaller chunks
3. Move operations to background threads
4. Add timeout mechanisms
5. Optimize memory usage

The logging system will pinpoint exactly where your game is freezing, making it much easier to fix the root cause.
